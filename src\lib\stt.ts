"use server";

export interface ChatCompletionMessageParam {
  role: "system" | "user" | "assistant";
  content: string;
}

function getApiKey() {
  return process.env.GROQ_API_KEY || "";
}

const AUDIO_ENDPOINT = "https://api.groq.com/openai/v1/audio/transcriptions";

// SDK currently (Jun 2025) does not expose audio endpoints, so leave fetch fallback.
export async function whisperTranscribe(audio: Blob): Promise<string> {
  const apiKey = getApiKey();
  if (!apiKey) throw new Error("Missing GROQ API key.");

  if (audio.size < 1000) {
    console.warn("Audio blob is very small:", audio.size, "bytes");
    return ".";
  }

  const form = new FormData();

  let filename = "audio.webm";
  if (audio.type.includes("mp4")) {
    filename = "audio.mp4";
  } else if (audio.type.includes("wav")) {
    filename = "audio.wav";
  } else if (audio.type.includes("ogg")) {
    filename = "audio.ogg";
  }

  form.append("file", audio, filename);
  form.append("model", "whisper-large-v3-turbo");

  const res = await fetch(AUDIO_ENDPOINT, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${apiKey}`,
    },
    body: form,
  });

  if (!res.ok) {
    const errorText = await res.text();
    console.error("Groq whisper error:", errorText);
    throw new Error(`Groq whisper error: ${errorText}`);
  }

  const data = await res.json();

  const transcribedText = data.text as string;

  if (!transcribedText || transcribedText.trim().length === 0) {
    console.warn("Empty transcription received");
    return ".";
  }

  return transcribedText;
}
