@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  @keyframes fade-in-up {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
    opacity: 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%; /* White */
    --foreground: 240 10% 3.9%; /* Nearly Black */
    --card: 0 0% 100%; /* White */
    --card-foreground: 240 10% 3.9%; /* Nearly Black */
    --popover: 0 0% 100%; /* White */
    --popover-foreground: 240 10% 3.9%; /* Nearly Black */
    --primary:158.1 64.4% 37%; /* A shade of green */
    --primary-foreground: hsl(220, 10%, 95%);
    --secondary: 220 14.3% 95.9%; /* Light gray */
    --secondary-foreground: 240 5.9% 10%; /* Dark gray */
    --muted: 220 14.3% 95.9%; /* Light gray */
    --muted-foreground: 240 3.8% 46.1%; /* Medium gray */
    --accent: 220 14.3% 95.9%; /* Light gray */
    --accent-foreground: 240 5.9% 10%; /* Dark gray */
    --destructive: 0 84.2% 60.2%; /* Red */
    --destructive-foreground: 0 0% 98%; /* White */
    --border: 220 13.1% 91%; /* Light gray border */
    --input: 220 13.1% 91%; /* Light gray input border */
    --ring: 142.1 76.2% 36.3%; /* Green ring color */
    --radius: 0.5rem;
  }
}