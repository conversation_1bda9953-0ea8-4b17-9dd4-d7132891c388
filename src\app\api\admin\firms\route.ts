import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { firms, questions } from "@/db/schema";
import { eq, and } from "drizzle-orm";

export async function GET() {
  try {
    const allFirms = await db.select().from(firms);
    return NextResponse.json(allFirms);
  } catch (error) {
    console.error("Error fetching firms:", error);
    return NextResponse.json({ error: "Failed to fetch firms" }, { status: 500 });
  }
}

import { v4 as uuidv4 } from "uuid";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { name } = body;

    if (!name) {
      return NextResponse.json({ error: "Firm name is required" }, { status: 400 });
    }

    const newFirm = {
      id: uuidv4(),
      name,
    };

    await db.insert(firms).values(newFirm);
    return NextResponse.json(newFirm, { status: 201 });
  } catch (error) {
    console.error("Error creating firm:", error);
    return NextResponse.json({ error: "Failed to create firm" }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json({ error: "Firm ID is required" }, { status: 400 });
    }

    // Check if there are any questions associated with this firm
    const associatedQuestions = await db
      .select()
      .from(questions)
      .where(eq(questions.firmId, id));

    if (associatedQuestions.length > 0) {
      return NextResponse.json(
        { error: "Cannot delete firm: It is associated with existing questions." },
        { status: 400 }
      );
    }

    const deletedFirm = await db.delete(firms).where(eq(firms.id, id)).returning();

    if (deletedFirm.length === 0) {
      return NextResponse.json({ error: "Firm not found" }, { status: 404 });
    }

    return NextResponse.json({ message: "Firm deleted successfully" }, { status: 200 });
  } catch (error) {
    console.error("Error deleting firm:", error);
    return NextResponse.json({ error: "Failed to delete firm" }, { status: 500 });
  }
}