import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Award } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>T<PERSON>le, CardDescription } from "@/components/ui/card";

interface Feature {
  icon: any;
  title: string;
  description: string;
}

const FEATURES: Feature[] = [
  {
    icon: Zap,
    title: "AI Mock Interviews",
    description:
      "Practice with realistic scenarios tailored to each firm's specific requirements and culture.",
  },
  {
    icon: BarChart3,
    title: "Instant Feedback",
    description:
      "Get detailed analysis of your responses with actionable improvement tips and scoring.",
  },
  {
    icon: Clock,
    title: "Real-Time Practice",
    description:
      "Simulate actual interview conditions with timed responses and pressure scenarios.",
  },
  {
    icon: Award,
    title: "Progress Tracking",
    description:
      "Monitor your improvement over time with detailed analytics and recommendations.",
  },
];

export default function LandingFeatures() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Why Choose Preparify?
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Our AI-powered platform gives you everything you need to succeed in Big 4 interviews
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {FEATURES.map((feature, index) => (
            <Card key={index} className="bg-white shadow-md">
              <CardContent className="text-center">
                <div className="bg-gradient-to-br from-emerald-100 to-teal-100 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:from-emerald-200 group-hover:to-teal-200 transition-all">
                  <feature.icon className="h-8 w-8 text-emerald-600" />
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </CardTitle>
                <CardDescription className="text-gray-600 leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}