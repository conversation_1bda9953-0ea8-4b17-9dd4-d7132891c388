"use client";

import { <PERSON>T<PERSON><PERSON>, <PERSON>, CheckCircle, Target } from "lucide-react";

interface CoverLetterLoadingProps {
  fileName?: string;
}

export function CoverLetterLoading({ fileName }: CoverLetterLoadingProps) {
  const steps = [
    {
      icon: <FileText className="w-5 h-5" />,
      text: "Extracting text content",
      delay: "0s"
    },
    {
      icon: <Brain className="w-5 h-5" />,
      text: "Analyzing structure and content",
      delay: "1s"
    },
    {
      icon: <Target className="w-5 h-5" />,
      text: "Evaluating tone and persuasiveness",
      delay: "2s"
    },
    {
      icon: <CheckCircle className="w-5 h-5" />,
      text: "Generating improvement recommendations",
      delay: "3s"
    }
  ];

  return (
    <div className="p-8">
      <div className="max-w-md mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="w-8 h-8 text-blue-600 animate-pulse" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Analyzing Your Cover Letter
          </h2>
          {fileName && (
            <p className="text-sm text-gray-600">
              Processing: {fileName}
            </p>
          )}
        </div>

        {/* Progress Steps */}
        <div className="space-y-4">
          {steps.map((step, index) => (
            <div
              key={index}
              className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 animate-fade-in"
              style={{ animationDelay: step.delay }}
            >
              <div className="flex-shrink-0 p-2 bg-blue-100 rounded-full text-blue-600">
                {step.icon}
              </div>
              <span className="text-gray-700">{step.text}</span>
              <div className="flex-1 flex justify-end">
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Progress Bar */}
        <div className="mt-8">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progress</span>
            <span>Analyzing...</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: "75%" }}></div>
          </div>
        </div>

        {/* Tips */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">While you wait...</h4>
          <p className="text-sm text-blue-800">
            Our AI is analyzing your cover letter's structure, tone, content quality, and alignment with best practices. 
            This usually takes 10-15 seconds.
          </p>
        </div>
      </div>
    </div>
  );
}
