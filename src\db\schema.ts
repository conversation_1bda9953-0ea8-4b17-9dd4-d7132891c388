import {
  timestamp,
  pgTable,
  text,
  primaryKey,
  integer,
  real,
  json,
} from "drizzle-orm/pg-core";
import type { AdapterAccount } from "next-auth/adapters";

export const user = pgTable("user", {
  id: text("id").primaryKey(),
  email: text("email").notNull().unique(),
  emailVerified: timestamp("emailVerified", { mode: "date" }),
  name: text("name"),
  image: text("image"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const accounts = pgTable(
  "account",
  {
    userId: text("userId")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
    type: text("type").$type<AdapterAccount["type"]>().notNull(),
    provider: text("provider").notNull(),
    providerAccountId: text("providerAccountId").notNull(),
    refresh_token: text("refresh_token"),
    access_token: text("access_token"),
    expires_at: integer("expires_at"),
    token_type: text("token_type"),
    scope: text("scope"),
    id_token: text("id_token"),
    session_state: text("session_state"),
  },
  (account) => ({
    compoundKey: primaryKey({
      columns: [account.provider, account.providerAccountId],
    }),
  })
);

export const firms = pgTable("firm", {
  id: text("id").primaryKey(),
  name: text("name").notNull().unique(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const questions = pgTable("question", {
  id: text("id").primaryKey(),
  questionText: text("question_text").notNull(),
  category: text("category").notNull(),
  firmId: text("firm_id")
    .notNull()
    .references(() => firms.id, { onDelete: "cascade" }),
  difficulty: text("difficulty").notNull(), // e.g., "Easy", "Medium", "Hard"
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const sessions = pgTable("session", {
  sessionToken: text("sessionToken").primaryKey(),
  userId: text("userId")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  expires: timestamp("expires", { mode: "date" }).notNull(),
});

export const verificationTokens = pgTable(
  "verificationToken",
  {
    identifier: text("identifier").notNull(),
    token: text("token").notNull(),
    expires: timestamp("expires", { mode: "date" }).notNull(),
  },
  (vt) => ({
    compoundKey: primaryKey({ columns: [vt.identifier, vt.token] }),
  })
);

export const interviewSessions = pgTable("interview_session", {
  id: text("id").primaryKey(),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  category: text("category").notNull(), // "technical" or "behavioral"
  firmId: text("firm_id")
    .notNull()
    .references(() => firms.id, { onDelete: "cascade" }),
  status: text("status").notNull().default("in_progress"), // "in_progress", "completed", "cancelled"
  startedAt: timestamp("started_at").defaultNow().notNull(),
  completedAt: timestamp("completed_at"),
  durationMinutes: integer("duration_minutes").default(2), // Time constraint in minutes
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const interviewReports = pgTable("interview_report", {
  id: text("id").primaryKey(),
  sessionId: text("session_id")
    .notNull()
    .references(() => interviewSessions.id, { onDelete: "cascade" }),
  overallScore: real("overall_score").notNull(),
  oneLineSummary: text("one_line_summary").notNull(),
  strengths: json("strengths").$type<string[]>().notNull(),
  areasForImprovement: json("areas_for_improvement")
    .$type<string[]>()
    .notNull(),
  averageResponseTime: real("average_response_time").notNull(),
  averageAnswerDuration: real("average_answer_duration").notNull(),
  totalFillerWords: integer("total_filler_words").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const questionResponses = pgTable("question_response", {
  id: text("id").primaryKey(),
  sessionId: text("session_id")
    .notNull()
    .references(() => interviewSessions.id, { onDelete: "cascade" }),
  questionId: text("question_id")
    .notNull()
    .references(() => questions.id, { onDelete: "cascade" }),
  questionText: text("question_text").notNull(), // Store the actual question text for historical purposes
  userAnswer: text("user_answer").notNull(),
  score: real("score").notNull(),
  feedback: text("feedback").notNull(),
  improvements: json("improvements").$type<string[]>().notNull(),
  responseTime: real("response_time"), // Time in seconds from question finished to user started speaking
  answerDuration: real("answer_duration"), // Time in seconds for the user's answer
  fillerWordCount: integer("filler_word_count"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});
