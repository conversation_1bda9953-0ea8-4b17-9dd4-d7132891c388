"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Calendar, Clock, Building2, FileText, AlertCircle } from "lucide-react";
import { format } from "date-fns";

interface InterviewSession {
  id: string;
  category: string;
  firm: string;
  status: string;
  startedAt: string;
  completedAt?: string;
  durationMinutes: number;
  hasReport: boolean;
}

export default function InterviewHistoryPage() {
  const router = useRouter();
  const [sessions, setSessions] = useState<InterviewSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        const response = await fetch("/api/interview/history");
        if (!response.ok) {
          throw new Error("Failed to fetch interview history");
        }
        const data = await response.json();
        setSessions(data.sessions);
      } catch (err) {
        console.error("Error fetching history:", err);
        setError("Failed to load interview history");
      } finally {
        setLoading(false);
      }
    };

    fetchHistory();
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
      case "in_progress":
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">In Progress</Badge>;
      case "cancelled":
        return <Badge variant="outline" className="bg-red-100 text-red-800">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getCategoryIcon = (category: string) => {
    return category === "technical" ? "🧠" : "👥";
  };

  const handleViewReport = (sessionId: string) => {
    router.push(`/dashboard/interview/report/${sessionId}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background-primary">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center gap-4 mb-8">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push("/dashboard/interview")}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Interview History</h1>
              <p className="text-text-secondary">Your past interview sessions</p>
            </div>
          </div>
          <div className="text-center py-8">Loading...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background-primary">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center gap-4 mb-8">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push("/dashboard/interview")}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Interview History</h1>
              <p className="text-text-secondary">Your past interview sessions</p>
            </div>
          </div>
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background-primary">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push("/dashboard/interview")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Interview History</h1>
            <p className="text-text-secondary">Your past interview sessions</p>
          </div>
        </div>

        {/* Sessions List */}
        <div className="max-w-4xl mx-auto">
          {sessions.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">No interviews yet</h3>
              <p className="text-gray-500 mb-6">Start your first interview to see your history here</p>
              <Button onClick={() => router.push("/dashboard/interview/select")}>
                Start Interview
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {sessions.map((session) => (
                <Card key={session.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{getCategoryIcon(session.category)}</div>
                        <div>
                          <CardTitle className="text-lg capitalize">
                            {session.category} Interview
                          </CardTitle>
                          <CardDescription className="flex items-center gap-2">
                            <Building2 className="w-4 h-4" />
                            {session.firm}
                          </CardDescription>
                        </div>
                      </div>
                      {getStatusBadge(session.status)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-6 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {format(new Date(session.startedAt), "MMM d, yyyy")}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          {format(new Date(session.startedAt), "h:mm a")}
                        </div>
                        {session.completedAt && (
                          <div className="text-green-600">
                            Duration: {Math.round((new Date(session.completedAt).getTime() - new Date(session.startedAt).getTime()) / 60000)} min
                          </div>
                        )}
                      </div>
                      {session.hasReport && session.status === "completed" && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewReport(session.id)}
                        >
                          View Report
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
