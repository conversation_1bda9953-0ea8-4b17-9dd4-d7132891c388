import Link from "next/link";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";

const FIRMS = [
  {
    name: "Deloitte",
    logo: "/icons/deloitte.svg",
    href: "/dashboard?firm=deloitte",
  },
  {
    name: "PwC",
    logo: "/icons/pwc.svg",
    href: "/dashboard?firm=pwc",
  },
  {
    name: "EY",
    logo: "/icons/ey.svg",
    href: "/dashboard?firm=ey",
  },
  {
    name: "KPMG",
    logo: "/icons/kpmg.svg",
    href: "/dashboard?firm=kpmg",
  },
];

export default function LandingFirmSelection() {
  return (
    <section id="firms" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Choose Your Target Firm
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Get personalized preparation tailored to your specific Big 4 firm
          </p>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
          {FIRMS.map((firm) => (
            <Link
              key={firm.name}
              href={firm.href}
              className="flex flex-col items-center p-8 bg-white rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 group border border-gray-100 hover:border-emerald-200 hover:-translate-y-1"
            >
              <div className="w-24 h-24 flex items-center justify-center mb-4">
                <Image
                  src={firm.logo || "/placeholder.svg"}
                  alt={firm.name + " logo"}
                  width={96}
                  height={96}
                  className="object-contain group-hover:scale-110 transition-transform duration-300"
                />
              </div>
              <span className="text-xl font-semibold text-gray-800 group-hover:text-emerald-600 transition-colors">
                {firm.name}
              </span>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}