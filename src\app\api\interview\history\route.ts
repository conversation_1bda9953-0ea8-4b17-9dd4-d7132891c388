import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { interviewSessions, firms, interviewReports } from "@/db/schema";
import { eq, desc, and, or, gte, lt } from "drizzle-orm";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Clean up old incomplete sessions (older than 1 hour and still in progress)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    await db
      .update(interviewSessions)
      .set({ status: "cancelled", updatedAt: new Date() })
      .where(
        and(
          eq(interviewSessions.userId, session.user.id),
          eq(interviewSessions.status, "in_progress"),
          lt(interviewSessions.startedAt, oneHourAgo)
        )
      );

    // Get user's interview sessions with firm information and report status
    // Only show completed sessions or very recent in-progress ones
    const sessions = await db
      .select({
        id: interviewSessions.id,
        category: interviewSessions.category,
        status: interviewSessions.status,
        startedAt: interviewSessions.startedAt,
        completedAt: interviewSessions.completedAt,
        durationMinutes: interviewSessions.durationMinutes,
        firmName: firms.name,
        hasReport: interviewReports.id,
      })
      .from(interviewSessions)
      .leftJoin(firms, eq(interviewSessions.firmId, firms.id))
      .leftJoin(
        interviewReports,
        eq(interviewSessions.id, interviewReports.sessionId)
      )
      .where(
        and(
          eq(interviewSessions.userId, session.user.id),
          or(
            eq(interviewSessions.status, "completed"),
            and(
              eq(interviewSessions.status, "in_progress"),
              gte(interviewSessions.startedAt, oneHourAgo)
            )
          )
        )
      )
      .orderBy(desc(interviewSessions.startedAt));

    const formattedSessions = sessions.map((s) => ({
      id: s.id,
      category: s.category,
      firm: s.firmName,
      status: s.status,
      startedAt: s.startedAt,
      completedAt: s.completedAt,
      durationMinutes: s.durationMinutes,
      hasReport: !!s.hasReport,
    }));

    return NextResponse.json({
      sessions: formattedSessions,
      count: formattedSessions.length,
    });
  } catch (error) {
    console.error("Error fetching interview history:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
