"use server";

import { z } from "zod";

// Schema for cover letter analysis, based on expert prompt
const CoverLetterAnalysisSchema = z.object({
  rating: z.number().int().min(0).max(100),
  generalFeedback: z.string(),
  lineByLineSuggestions: z.array(
    z.object({
      line: z.string(),
      issue: z.string(),
      suggestion: z.string(),
    })
  ),
  strengths: z.array(z.string()),
  weaknesses: z.array(z.string()),
  structure: z.object({
    hasOpening: z.boolean(),
    hasBody: z.boolean(),
    hasClosing: z.boolean(),
    hasCallToAction: z.boolean(),
  }),
  tone: z.enum(["Professional", "Casual", "Mixed", "Unclear"]),
  wordCount: z.number(),
  recommendedActions: z.array(
    z.object({
      action: z.string(),
      priority: z.enum(["Critical", "High", "Medium", "Low"]),
      expectedImpact: z.string(),
    })
  ),
});

export type CoverLetterAnalysisResult = z.infer<
  typeof CoverLetterAnalysisSchema
>;

// Extract text from PDF using pdf-parse
async function extractTextFromPDF(pdfBuffer: Buffer): Promise<string> {
  try {
    const pdfParse = (await import("pdf-parse")).default;

    const data = await pdfParse(pdfBuffer, {
      max: 50, // Maximum number of pages to parse
    });

    if (!data.text || data.text.trim().length === 0) {
      throw new Error(
        "No text found in PDF - document may be image-based or corrupted"
      );
    }

    return data.text;
  } catch (error) {
    console.error("Error extracting text from PDF:", error);

    if (error instanceof Error) {
      if (error.message.includes("ENOENT")) {
        throw new Error(
          "PDF processing error: File access issue. Please try uploading the file again."
        );
      }
      if (error.message.includes("Invalid PDF")) {
        throw new Error(
          "Invalid PDF format. Please ensure the file is a valid PDF document."
        );
      }
      if (error.message.includes("No text found")) {
        throw new Error(
          "Cannot extract text from PDF. The document may be image-based or password-protected."
        );
      }
    }

    throw new Error(
      "Failed to extract text from PDF. Please try a different PDF file."
    );
  }
}

export async function analyzeCoverLetterFromFile(
  formData: FormData
): Promise<
  { success: true; analysis: CoverLetterAnalysisResult } | { error: string }
> {
  const file = formData.get("coverLetter") as File;

  if (!file) {
    return { error: "No file uploaded." };
  }

  // Validate file type
  const allowedTypes = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "image/webp",
    "text/plain",
  ];

  if (!allowedTypes.includes(file.type)) {
    return {
      error:
        "Unsupported file type. Please upload a PDF, image (JPG, PNG, WebP), or text file.",
    };
  }

  // Validate file size (10MB limit)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    return {
      error: "File size too large. Please upload a file smaller than 10MB.",
    };
  }

  try {
    let coverLetterContent: string;
    let contentType: "text" | "image";

    const arrayBuffer = await file.arrayBuffer();

    if (file.type === "text/plain") {
      // Handle text files
      coverLetterContent = new TextDecoder().decode(arrayBuffer);
      contentType = "text";
    } else if (file.type === "application/pdf") {
      // Handle PDF files
      const pdfBuffer = Buffer.from(arrayBuffer);

      coverLetterContent = await extractTextFromPDF(pdfBuffer);
      contentType = "text";
    } else {
      // Handle image files - convert to base64 for AI processing
      const base64 = Buffer.from(arrayBuffer).toString("base64");
      coverLetterContent = `data:${file.type};base64,${base64}`;
      contentType = "image";
    }

    // Analyze the extracted content
    const result = await analyzeCoverLetterContent(
      coverLetterContent,
      contentType
    );

    if (result.success && result.analysis) {
      return { success: true, analysis: result.analysis };
    } else {
      return { error: result.error || "Failed to analyze cover letter" };
    }
  } catch (error) {
    console.error("Error processing cover letter file:", error);

    if (error instanceof Error) {
      return { error: error.message };
    }

    return {
      error: "An unexpected error occurred while processing your file.",
    };
  }
}

export async function analyzeCoverLetter(content: string): Promise<{
  success: boolean;
  analysis?: CoverLetterAnalysisResult;
  error?: string;
}> {
  return analyzeCoverLetterContent(content, "text");
}

async function analyzeCoverLetterContent(
  content: string,
  contentType: "text" | "image" = "text"
): Promise<{
  success: boolean;
  analysis?: CoverLetterAnalysisResult;
  error?: string;
}> {
  try {
    // Basic content validation
    if (!content || content.trim().length < 50) {
      return {
        success: false,
        error:
          "Cover letter content is too short. Please provide at least 50 characters.",
      };
    }

    const models = ["llama-3.1-8b-instant"];

    for (const model of models) {
      try {
        const userContent =
          contentType === "text"
            ? `Please analyze this cover letter and provide detailed feedback:\n\n${content.substring(
                0,
                4000
              )}`
            : [
                {
                  type: "text",
                  text: "Please analyze this cover letter image and provide detailed feedback in the exact JSON format specified.",
                },
                {
                  type: "image_url",
                  image_url: {
                    url: content,
                  },
                },
              ];

        const messages = [
          {
            role: "system",
            content: `You are a world-class career strategist and storytelling coach with over a decade of experience helping candidates secure offers at the Big 4 (EY, PwC, KPMG, Deloitte) and MBB (McKinsey, BCG, Bain). You’ve reviewed thousands of successful cover letters and know what separates forgettable from phenomenal.

GOAL:
Review and transform student or entry-level cover letters into unforgettable, recruiter-stopping narratives. Your job is not to rehash the resume — your job is to elevate it.

CORE PHILOSOPHY:
A great cover letter tells a story. It weaves together the candidate’s motivation, personality, relevant experience, and knowledge of the company — while creating a compelling, human case for why they belong there.

HANDLING UNCONVENTIONAL INPUTS:
If the provided text does not resemble a standard cover letter (e.g., it's a resume, a list of bullet points, or random text), your primary goal is to identify this and guide the user.
- In "generalFeedback", state clearly that the document is not a standard cover letter (e.g., "This document appears to be a resume, not a cover letter."). Explain why a narrative cover letter is more effective.
- For "lineByLineSuggestions", instead of flagging every line as "wrong," group related items (like skills, contact info, or a job entry) and provide a single suggestion explaining how to weave that information into a cover letter paragraph. For example, for a list of skills, the suggestion should be: "Instead of a list, demonstrate 1-2 of these skills in a story. For example: 'My experience with Python and Next.js was crucial in developing...'".
- Base the "rating" on its quality *as a cover letter*. A resume submitted as a cover letter should receive a very low score (e.g., under 30).
- The "weaknesses" should include "Not a cover letter" and "Lacks a narrative structure."

EVALUATION FRAMEWORK (Weighted):
1. *Structure & Format (20%)*
   - Clear opening that states the role and hooks attention
   - Body paragraphs with a logical flow (past, present, future)
   - Clear, action-oriented close with a call to action
   - 250–400 words max

2. *Narrative & Personal Storytelling (30%)*
   - Personal motivation for applying (not copy-paste fluff)
   - Unique value proposition — why this person stands out
   - Story of growth, grit, curiosity, or relevant challenges
   - Shows—not just tells—what makes them right for the role

3. *Company & Role Alignment (20%)*
   - References to company values, recent work, or mission
   - Knowledge of the specific role and its expectations
   - Clear understanding of how the candidate adds value

4. *Tone & Style (15%)*
   - Confident, sincere, and human voice
   - Active voice and vivid language
   - No jargon, fluff, or vague adjectives
   - Sounds like a real person, not a robot

5. *ATS & Professional Standards (15%)*
   - ATS-friendly formatting (no text boxes or graphics)
   - Keywords naturally integrated (e.g., “audit,” “client-facing,” “financial analysis”)
   - No spelling or grammar issues

RESPONSE FORMAT (Strict JSON only — no extra commentary):
{
  "rating": <0-100>,
  "generalFeedback": "<2-3 paragraphs of comprehensive feedback focused on structure, story, and impact>",
  "lineByLineSuggestions": [
    {
      "line": "<copy of original sentence or paragraph>",
      "issue": "<what’s wrong or missing in this line>",
      "suggestion": "<how to rewrite it to improve storytelling, tone, or impact>"
    }
  ],
  "strengths": ["<clear strength 1>", "<clear strength 2>", "<clear strength 3>"],
  "weaknesses": ["<clear weakness 1>", "<clear weakness 2>", "<clear weakness 3>"],
  "structure": {
    "hasOpening": <true/false>,
    "hasBody": <true/false>,
    "hasClosing": <true/false>,
    "hasCallToAction": <true/false>
  },
  "tone": "<Professional/Casual/Mixed/Unclear>",
  "wordCount": <number>,
  "recommendedActions": [
    {
      "action": "<concrete revision or writing exercise to improve the letter>",
      "priority": "<Critical/High/Medium/Low>",
      "expectedImpact": "<why this change matters>"
    }
  ]
}

CATEGORIES FOR lineByLineSuggestions:
- Structure & Flow
- Narrative Depth
- Role/Company Alignment
- Tone & Voice
- Clarity & Conciseness
- ATS Optimization

IMPORTANT:
- Use only valid JSON (double quotes, no trailing commas)
- Escape all strings properly
- Never add explanations or markdown outside the JSON
- All feedback must be specific, actionable, and grounded in industry expectations for elite firms`,
          },
          {
            role: "user",
            content: userContent,
          },
        ];

        const response = await fetch(
          "https://api.groq.com/openai/v1/chat/completions",
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${process.env.GROQ_API_KEY}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              model,
              messages,
              temperature: 0.1,
              max_tokens: 1500,
              response_format: { type: "json_object" },
            }),
          }
        );

        if (!response.ok) {
          const errorBody = await response.text();
          console.error(
            `API error with model ${model}: ${response.status}`,
            errorBody
          );
          continue;
        }

        const data = await response.json();
        const aiResponse = data.choices?.[0]?.message?.content;

        if (!aiResponse) {
          console.error(`No response from model ${model}`);
          continue;
        }

        const parsedAnalysis = JSON.parse(aiResponse);
        const validatedAnalysis =
          CoverLetterAnalysisSchema.parse(parsedAnalysis);

        return { success: true, analysis: validatedAnalysis };
      } catch (error) {
        console.error(`Error with model ${model}:`, error);
        continue;
      }
    }

    return {
      success: false,
      error: "Failed to analyze cover letter. Please try again.",
    };
  } catch (error) {
    console.error("Cover letter analysis error:", error);
    return {
      success: false,
      error: "Failed to analyze cover letter. Please try again.",
    };
  }
}
