"use client"

import type React from "react"

import { <PERSON>T<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, CheckCircle } from "lucide-react"

interface LoadingAnimationProps {
  fileName?: string
}

export function LoadingAnimation({ fileName }: LoadingAnimationProps) {
  return (
    <div className="p-12 text-center">
      <div className="max-w-md mx-auto">
        {/* Animated File Icon */}
        <div className="relative mb-8">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto animate-pulse">
            <FileText className="w-10 h-10 text-green-600" />
          </div>
          <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center animate-bounce">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
        </div>

        {/* File Name */}
        {fileName && (
          <div className="mb-8 p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600 truncate">{fileName}</p>
          </div>
        )}

        {/* Loading Steps */}
        <div className="space-y-4 mb-8">
          <LoadingStep icon={<FileText className="w-5 h-5" />} text="Processing your resume..." isActive={true} />
          <LoadingStep
            icon={<Brain className="w-5 h-5" />}
            text="AI analysis in progress..."
            isActive={true}
            delay={1000}
          />
          <LoadingStep
            icon={<CheckCircle className="w-5 h-5" />}
            text="Generating insights..."
            isActive={true}
            delay={2000}
          />
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div
            className="bg-green-500 h-2 rounded-full animate-pulse"
            style={{
              width: "75%",
              animation: "loading-progress 3s ease-in-out infinite",
            }}
          ></div>
        </div>

        <p className="text-gray-600">This usually takes 30-60 seconds</p>
      </div>

      <style jsx>{`
        @keyframes loading-progress {
          0% { width: 10%; }
          50% { width: 75%; }
          100% { width: 95%; }
        }
      `}</style>
    </div>
  )
}

interface LoadingStepProps {
  icon: React.ReactNode
  text: string
  isActive: boolean
  delay?: number
}

function LoadingStep({ icon, text, isActive, delay = 0 }: LoadingStepProps) {
  return (
    <div
      className={`flex items-center gap-3 p-3 rounded-lg transition-all duration-500 ${
        isActive ? "bg-green-50 text-green-700" : "text-gray-400"
      }`}
      style={{ animationDelay: `${delay}ms` }}
    >
      <div className={`${isActive ? "animate-spin" : ""}`}>{icon}</div>
      <span className="font-medium">{text}</span>
    </div>
  )
}
