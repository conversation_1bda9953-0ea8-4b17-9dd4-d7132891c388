"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface UserStats {
  totalInterviews: number;
  totalQuestions: number;
  averageScore: number;
  practiceStreak: number;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch("/api/user/stats");
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        }
      } catch (error) {
        console.error("Error fetching stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return (
    <main className="min-h-screen bg-gray-50 py-20 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            Welcome to Preparify
          </h1>
          <p className="text-gray-600 text-lg">
            Get ready for your next career move
          </p>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          {loading ? (
            // Loading skeleton cards
            Array.from({ length: 4 }).map((_, index) => (
              <Card key={index} className="animate-pulse">
                <CardHeader className="pb-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
              </Card>
            ))
          ) : stats ? (
            // Actual stats cards with staggered animation
            <>
              <Card
                className="animate-fade-in-up"
                style={{ animationDelay: "0.1s" }}
              >
                <CardHeader className="pb-2">
                  <CardDescription>Total Interviews</CardDescription>
                  <CardTitle className="text-3xl text-emerald-600">
                    {stats.totalInterviews}
                  </CardTitle>
                </CardHeader>
              </Card>

              <Card
                className="animate-fade-in-up"
                style={{ animationDelay: "0.2s" }}
              >
                <CardHeader className="pb-2">
                  <CardDescription>Questions Practiced</CardDescription>
                  <CardTitle className="text-3xl text-blue-600">
                    {stats.totalQuestions}
                  </CardTitle>
                </CardHeader>
              </Card>

              <Card
                className="animate-fade-in-up"
                style={{ animationDelay: "0.3s" }}
              >
                <CardHeader className="pb-2">
                  <CardDescription>Average Score</CardDescription>
                  <CardTitle className="text-3xl text-purple-600">
                    {stats.averageScore > 0
                      ? `${stats.averageScore}/10`
                      : "N/A"}
                  </CardTitle>
                </CardHeader>
              </Card>

              <Card
                className="animate-fade-in-up"
                style={{ animationDelay: "0.4s" }}
              >
                <CardHeader className="pb-2">
                  <CardDescription>Practice Streak</CardDescription>
                  <CardTitle className="text-3xl text-orange-600">
                    {stats.practiceStreak} days
                  </CardTitle>
                </CardHeader>
              </Card>
            </>
          ) : (
            // Error state - show empty cards
            Array.from({ length: 4 }).map((_, index) => (
              <Card key={index}>
                <CardHeader className="pb-2">
                  <CardDescription>No data available</CardDescription>
                  <CardTitle className="text-3xl text-gray-400">--</CardTitle>
                </CardHeader>
              </Card>
            ))
          )}
        </div>

        {/* Main Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <Link
            href="/dashboard/resume"
            className="group bg-white p-8 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:border-emerald-100 hover:scale-105"
          >
            <div className="bg-emerald-50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-emerald-100 transition-colors">
              <svg
                className="w-8 h-8 text-emerald-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2 text-center">
              Resume Review
            </h2>
            <p className="text-gray-600 text-center">
              Get detailed feedback on your resume from AI
            </p>
          </Link>

          <Link
            href="/dashboard/cover-letter"
            className="group bg-white p-8 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:border-purple-100 hover:scale-105"
          >
            <div className="bg-purple-50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-100 transition-colors">
              <svg
                className="w-8 h-8 text-purple-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2 text-center">
              Cover Letter Review
            </h2>
            <p className="text-gray-600 text-center">
              Analyze and improve your cover letter content
            </p>
          </Link>

          <Link
            href="/dashboard/interview"
            className="group bg-white p-8 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:border-blue-100 hover:scale-105"
          >
            <div className="bg-blue-50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-100 transition-colors">
              <svg
                className="w-8 h-8 text-blue-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2 text-center">
              Interview Practice
            </h2>
            <p className="text-gray-600 text-center">
              Practice with AI-powered interview simulations
            </p>
          </Link>
        </div>
      </div>
    </main>
  );
}
