<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1920px" height="1080px" viewBox="0 0 1920 1080" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>CaptionCraft Dashboard</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#E6F7FF" offset="0%"></stop>
            <stop stop-color="#CCE5FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Dashboard-Preview" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="Background" fill="url(#linearGradient-1)" x="0" y="0" width="1920" height="1080"></rect>
        <rect id="Header" fill="#FFFFFF" x="0" y="0" width="1920" height="80"></rect>
        <rect id="Sidebar" fill="#FFFFFF" x="0" y="80" width="280" height="1000"></rect>
        <rect id="Content-Area" fill="#FFFFFF" x="320" y="120" width="1560" height="920" rx="8"></rect>
        
        <!-- Header Elements -->
        <text id="Logo" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#0066CC">
            <tspan x="40" y="50">CaptionCraft</tspan>
        </text>
        <circle id="Avatar" fill="#0066CC" cx="1840" cy="40" r="24"></circle>
        
        <!-- Sidebar Elements -->
        <rect id="Nav-Item-1" fill="#E6F7FF" x="20" y="120" width="240" height="50" rx="4"></rect>
        <rect id="Nav-Item-2" fill="#FFFFFF" x="20" y="180" width="240" height="50" rx="4"></rect>
        <rect id="Nav-Item-3" fill="#FFFFFF" x="20" y="240" width="240" height="50" rx="4"></rect>
        <rect id="Nav-Item-4" fill="#FFFFFF" x="20" y="300" width="240" height="50" rx="4"></rect>
        
        <!-- Content Area Elements -->
        <text id="Dashboard-Title" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#333333">
            <tspan x="360" y="180">Dashboard</tspan>
        </text>
        
        <!-- Stats Cards -->
        <rect id="Card-1" fill="#F8F9FA" x="360" y="220" width="300" height="160" rx="8"></rect>
        <rect id="Card-2" fill="#F8F9FA" x="680" y="220" width="300" height="160" rx="8"></rect>
        <rect id="Card-3" fill="#F8F9FA" x="1000" y="220" width="300" height="160" rx="8"></rect>
        
        <!-- Recent Images Section -->
        <text id="Recent-Images" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333333">
            <tspan x="360" y="440">Recent Images</tspan>
        </text>
        
        <!-- Image Grid -->
        <rect id="Image-1" fill="#E6F7FF" x="360" y="480" width="280" height="280" rx="8"></rect>
        <rect id="Image-2" fill="#E6F7FF" x="660" y="480" width="280" height="280" rx="8"></rect>
        <rect id="Image-3" fill="#E6F7FF" x="960" y="480" width="280" height="280" rx="8"></rect>
        <rect id="Image-4" fill="#E6F7FF" x="1260" y="480" width="280" height="280" rx="8"></rect>
        
        <!-- Generate Button -->
        <rect id="Generate-Button" fill="#0066CC" x="360" y="800" width="200" height="50" rx="4"></rect>
        <text id="Generate-Text" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#FFFFFF" text-anchor="middle">
            <tspan x="460" y="830">Generate New Image</tspan>
        </text>
    </g>
</svg>
