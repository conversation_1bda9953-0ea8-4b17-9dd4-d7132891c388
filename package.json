{"name": "next-saas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint .", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio --port 3001", "db:migrate": "ts-node src/scripts/run-migrations.ts"}, "dependencies": {"@auth/drizzle-adapter": "^1.8.0", "@hookform/resolvers": "^5.1.1", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.31.0", "@types/nodemailer": "^6.4.17", "ahooks": "^3.8.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^4.1.0", "drizzle-orm": "^0.41.0", "framer-motion": "^12.6.2", "groq-sdk": "^0.3.3", "lenis": "^1.2.3", "lucide-react": "^0.483.0", "motion": "^12.6.3", "nanoid": "^5.1.5", "next": "^15.3.3", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "nodemailer": "^6.10.1", "openai": "^5.8.2", "pdf-parse": "^1.1.1", "pg": "^8.11.3", "postgres": "^3.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.4.1", "sonner": "^2.0.5", "tailwind-merge": "^2.2.0", "tw-animate-css": "^1.2.4", "uuid": "^9.0.1", "zod": "^3.25.67", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/eslintrc": "^2.0.0", "@heygen/streaming-avatar": "^2.0.16", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.19.1", "@types/pdf-parse": "^1.1.5", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.21", "dotenv": "^16.3.1", "drizzle-kit": "^0.31.1", "eslint": "^8.0.0", "eslint-config-next": "latest", "postcss": "^8.4.31", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3"}}