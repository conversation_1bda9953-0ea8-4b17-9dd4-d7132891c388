import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { questions, firms } from "@/db/schema";
import { v4 as uuidv4 } from "uuid";
import { eq } from "drizzle-orm";

// A simple CSV parser for demonstration. In a real app, consider a robust library.
async function parseCsv(csvString: string): Promise<any[]> {
  const lines = csvString.trim().split('\n');
  if (lines.length === 0) return [];

  const headers = lines[0].split(',').map(h => h.trim());
  const data = lines.slice(1).map(line => {
    const values = line.split(',').map(v => v.trim());
    const row: { [key: string]: string } = {};
    headers.forEach((header, index) => {
      row[header] = values[index];
    });
    return row;
  });
  return data;
}

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as Blob | null;

    if (!file) {
      return NextResponse.json({ error: "No file uploaded" }, { status: 400 });
    }

    const fileContent = await file.text();
    const parsedData = await parseCsv(fileContent);

    if (parsedData.length === 0) {
      return NextResponse.json({ message: "No data found in CSV" }, { status: 200 });
    }

    const questionsToInsert = [];
    for (const row of parsedData) {
      const { questionText, category, firmName, difficulty } = row;

      if (!questionText || !category || !firmName || !difficulty) {
        console.warn("Skipping row due to missing fields:", row);
        continue;
      }

      let firm = await db.select().from(firms).where(eq(firms.name, firmName)).limit(1);
      let firmIdToUse: string;

      if (firm.length === 0) {
        const newFirmId = uuidv4();
        await db.insert(firms).values({ id: newFirmId, name: firmName });
        firmIdToUse = newFirmId;
      } else {
        firmIdToUse = firm[0].id;
      }

      questionsToInsert.push({
        id: uuidv4(),
        questionText,
        category,
        firmId: firmIdToUse,
        difficulty,
      });
    }

    if (questionsToInsert.length > 0) {
      await db.insert(questions).values(questionsToInsert);
    }

    return NextResponse.json({ message: `Successfully uploaded ${questionsToInsert.length} questions` }, { status: 200 });
  } catch (error) {
    console.error("Error uploading questions CSV:", error);
    return NextResponse.json({ error: "Failed to upload questions" }, { status: 500 });
  }
}