"use server";

import { type ChatCompletionMessageParam } from "@/lib/stt";
import Groq from "groq-sdk";
import { type ChatMessage } from "@/utils/interviewStorage";

type QuestionObject = { id: string; text: string };
type QuestionsByCategory = Record<string, QuestionObject[]>;

async function llamaChatInterview(
  messages: ChatCompletionMessageParam[]
): Promise<any> {
  const apiKey = process.env.GROQ_API_KEY || "";
  if (!apiKey) {
    console.warn("GROQ_API_KEY is not set");
    throw new Error("Groq API key not configured");
  }

  const groq = new Groq({ apiKey });

  const response = await groq.chat.completions.create({
    model: "llama-3.1-8b-instant",
    messages: messages as any[],
    temperature: 0.7,
    max_tokens: 2000,
    response_format: { type: "json_object" },
  });

  const content = response.choices[0]?.message?.content;
  if (!content) {
    throw new Error("No content in response from AI");
  }

  try {
    return JSON.parse(content);
  } catch (e) {
    console.error("Failed to parse response as JSON. Content was:", content);
    throw new Error("Failed to parse AI response as JSON.");
  }
}

function analyzeInterviewProgress(
  chatHistory: ChatMessage[],
  availableQuestions: QuestionsByCategory,
  timeBasedInterview: boolean = true
) {
  const askedQuestions = new Set<string>();
  const askedQuestionIds = new Set<string>();

  // Track asked questions by both text and ID for robust duplicate detection
  for (const message of chatHistory) {
    if (message.from === "avatar" && message.type === "question") {
      askedQuestions.add(message.message);
      if (message.questionId) {
        askedQuestionIds.add(message.questionId);
      }
    }
  }

  const totalQuestionsAsked = askedQuestions.size;

  // For time-based interviews, we don't need complex category logic
  if (timeBasedInterview) {
    return {
      askedQuestions,
      askedQuestionIds,
      totalQuestionsAsked,
      shouldEnd: false, // Time-based interviews don't end based on question count
    };
  }

  // Legacy support for non-time-based interviews (if needed)
  const availableCategories = Object.keys(availableQuestions);
  const shouldEnd = totalQuestionsAsked >= 10; // Arbitrary limit for non-time-based

  return {
    askedQuestions,
    askedQuestionIds,
    totalQuestionsAsked,
    shouldEnd,
  };
}

function buildDynamicInterviewPrompt(
  availableQuestions: QuestionsByCategory,
  chatHistory: ChatMessage[],
  timeBasedInterview: boolean = true
): string {
  const analysis = analyzeInterviewProgress(
    chatHistory,
    availableQuestions,
    timeBasedInterview
  );

  const isFirstQuestion = chatHistory.length === 0;
  const lastUserMessage = chatHistory.findLast(
    (m) => m.from === "user"
  )?.message;

  // Get the interview category (should be consistent since it's single-category)
  const interviewCategory = Object.keys(availableQuestions)[0] || "general";

  // Filter out already asked questions using both text and ID matching
  const unaskedQuestions = Object.values(availableQuestions)
    .flat()
    .filter((q) => {
      // Check both text and ID to prevent duplicates
      const textAlreadyAsked = analysis.askedQuestions.has(q.text);
      const idAlreadyAsked = q.id && analysis.askedQuestionIds.has(q.id);
      return !textAlreadyAsked && !idAlreadyAsked;
    });

  const shouldEnd = analysis.shouldEnd || unaskedQuestions.length === 0;

  return `You are Shawn, a professional Big 4 interviewer conducting a mock interview for ${interviewCategory} questions. Your goal is to create a natural, conversational interview experience.

INTERVIEW STATE:
- Questions asked: ${analysis.totalQuestionsAsked}
- Available unasked questions: ${unaskedQuestions.length}
- Should End Interview: ${shouldEnd}

AVAILABLE QUESTIONS (unasked only):
${JSON.stringify(
  unaskedQuestions.map((q) => q.text),
  null,
  2
)}

CONVERSATION CONTEXT:
${
  isFirstQuestion
    ? "This is the start of the interview."
    : `The candidate just said: "${lastUserMessage}"`
}

YOUR TASK:
${
  shouldEnd
    ? `The interview should end now. Either no more questions are available or time constraints apply.
 Respond with type "end" and provide a professional closing message thanking the candidate.`
    : `Continue the interview:
1. ${
        isFirstQuestion
          ? "Start with a warm welcome and mention this is a " +
            interviewCategory +
            " interview."
          : "Acknowledge the user's response naturally (1-2 sentences)."
      }
2. Ask ONE new question from the available questions list above.
3. Make the transition smooth and conversational.`
}

RESPONSE FORMAT (JSON only, no other text):
{
 "message": "${
   shouldEnd
     ? "Professional closing message."
     : "Your acknowledgment + chosen question."
 }",
 "type": "${shouldEnd ? "end" : "question"}",
 "category": "${shouldEnd ? "null" : interviewCategory}"
}

CRITICAL RULES:
- If shouldEnd is true, you MUST respond with type "end".
- ONLY ask questions from the "AVAILABLE QUESTIONS" list above.
- NEVER repeat a question that has already been asked.
- Your entire response MUST be a single, valid JSON object.
- The question you ask must match EXACTLY one of the questions in the available list.`;
}

export async function getNextAvatarMessage(
  chatHistory: ChatMessage[],
  availableQuestions: QuestionsByCategory,
  questionsPerCategory: number = 1, // Keep for backward compatibility but not used
  timeBasedInterview: boolean = true
): Promise<ChatMessage> {
  try {
    // For time-based interviews, we don't end based on question count
    // The calling code will handle time limits
    if (!timeBasedInterview) {
      const analysis = analyzeInterviewProgress(
        chatHistory,
        availableQuestions,
        timeBasedInterview
      );
      if (analysis.shouldEnd) {
        return {
          from: "avatar",
          message:
            "Thank you for taking the time to speak with me today. That concludes our interview. We'll review your responses and be in touch soon. Have a great day!",
          timestamp: Date.now(),
          type: "end",
          category: null,
        };
      }
    }

    const systemPrompt = buildDynamicInterviewPrompt(
      availableQuestions,
      chatHistory,
      timeBasedInterview
    );

    const llmMessages: ChatCompletionMessageParam[] = [
      { role: "system", content: systemPrompt },
      {
        role: "user",
        content:
          chatHistory.length === 0
            ? "Please start the interview."
            : `My last response was: "${
                chatHistory[chatHistory.length - 1].message
              }". Please continue the interview.`,
      },
    ];

    const resp = await llamaChatInterview(llmMessages);

    if (resp.type === "end") {
      return {
        from: "avatar",
        message:
          resp.message || "Thank you for your time. The interview is complete.",
        timestamp: Date.now(),
        type: "end",
        category: null,
      };
    }

    // Find the question ID by matching the response message with available questions
    let questionId: string | undefined;
    for (const [, questions] of Object.entries(availableQuestions)) {
      const matchingQuestion = questions.find((q) => {
        // Try exact match first
        if (q.text === resp.message) return true;
        // Try if the response contains the question
        if (resp.message.includes(q.text)) return true;
        // Try if the question is contained in the response (for cases where LLM adds context)
        if (q.text.includes(resp.message)) return true;
        // Try fuzzy matching by checking if key words match
        const questionWords = q.text
          .toLowerCase()
          .split(/\s+/)
          .filter((w: string) => w.length > 3);
        const responseWords = resp.message
          .toLowerCase()
          .split(/\s+/)
          .filter((w: string) => w.length > 3);
        const matchingWords = questionWords.filter((word: string) =>
          responseWords.some(
            (rWord: string) => rWord.includes(word) || word.includes(rWord)
          )
        );
        // If more than 50% of significant words match, consider it a match
        return matchingWords.length > questionWords.length * 0.5;
      });

      if (matchingQuestion) {
        questionId = matchingQuestion.id;
        break;
      }
    }

    if (!questionId) {
      console.warn(`No question ID found for LLM response: "${resp.message}"`);
    }

    return {
      from: "avatar",
      message: resp.message,
      timestamp: Date.now(),
      type: "question",
      category: resp.category || null,
      questionId,
    };
  } catch (error) {
    console.error(
      "Error in getNextAvatarMessage, throwing error to be handled by UI:",
      error
    );
    throw error;
  }
}

export interface InterviewReport {
  overallScore: number;
  oneLineSummary: string;
  strengths: string[];
  areasForImprovement: string[];
  averageResponseTime: number;
  averageAnswerDuration: number;
  totalFillerWords: number;
  questionAnalysis: Array<{
    questionId?: string;
    question: string;
    category: string;
    answer: string;
    score: number;
    feedback: string;
    improvements: string[];
    responseTime?: number;
    answerDuration?: number;
    fillerWordCount?: number;
  }>;
}

function buildEnhancedReportPrompt(chatHistory: ChatMessage[]): string {
  const questionAnswerPairs = [];
  for (let i = 0; i < chatHistory.length - 1; i++) {
    const current = chatHistory[i];
    const next = chatHistory[i + 1];

    if (
      current.from === "avatar" &&
      current.type === "question" &&
      next.from === "user"
    ) {
      questionAnswerPairs.push({
        questionId: current.questionId,
        question: current.message,
        category: current.category || "general",
        answer: {
          text: next.message,
          responseTime: next.responseTime,
          answerDuration: next.answerDuration,
          fillerWordCount: next.fillerWordCount,
        },
      });
    }
  }

  return `You are a career coach specialized in helping students land audit internships and full-time roles at Big 4 accounting firms. You’ve received a transcript of a mock interview including answer timing, filler word metrics, and the user’s full responses.

TRANSCRIPT:
${JSON.stringify(questionAnswerPairs, null, 2)}

YOUR TASK:
Analyze the user’s answers and delivery based on:

Pacing (response time, duration)
Clarity (filler words, rambling)
Structure & Relevance (use of STAR, answering the question directly)
Fit for Big 4 (professional tone, audit-focused mindset)

Be detailed, specific, and constructive. Avoid generic advice. All feedback must be based on the actual transcript. Encourage, but don’t sugarcoat.

RESPONSE FORMAT (JSON only, no other text):
{
  "overallScore": <number, 1-10, average of question scores>,
  "oneLineSummary": "<string, a concise one-line summary of the performance>",
  "strengths": [
    "<string, a key strength observed in the user's answers>",
    "<string, another key strength>"
  ],
  "areasForImprovement": [
    "<string, a key area for improvement based on the answers>",
    "<string, another area for improvement>"
  ],
  "questionAnalysis": [
    {
      "questionId": "<string, the original ID of the question>",
      "question": "<string, the question that was asked>",
      "category": "<string, the category of the question>",
      "answer": "<string, the user's answer>",
      "score": <number, 1-10>,
      "feedback": "<string, detailed, constructive feedback on this specific answer, what could be imrpoved (focus on this), also including comments on pacing and filler words>",
      "improvements": [
        "<string, a specific suggestion to improve this answer>"
      ]
    }
  ]
}

SCORING GUIDELINES:
- 9-10: Exceptional answer. Clear, concise, and compelling.
- 7-8: Good answer. Solid content with minor room for refinement.
- 5-6: Average answer. Addresses the question but lacks depth or clarity.
- 3-4: Below average. Significant parts of the answer are unclear or weak.
- 1-2: Poor answer. Fails to address the core of the question.

REQUIREMENTS:
- Base all feedback directly on the provided answers and metrics.
- Be encouraging but honest.
- Focus on actionable advice.
- Must return valid JSON only.`;
}

export async function generateInterviewReport(
  chatHistory: ChatMessage[]
): Promise<InterviewReport> {
  const prompt = buildEnhancedReportPrompt(chatHistory);
  const llmMessages: ChatCompletionMessageParam[] = [
    { role: "system", content: prompt },
    {
      role: "user",
      content:
        "Please analyze this interview session and generate a comprehensive performance report with scores and actionable feedback.",
    },
  ];

  const resp = await llamaChatInterview(llmMessages);

  const userAnswers = chatHistory.filter(
    (m) => m.from === "user" && m.message
  );
  const totalFillerWords = userAnswers.reduce(
    (sum, m) => sum + (m.fillerWordCount || 0),
    0
  );
  const totalResponseTime = userAnswers.reduce(
    (sum, m) => sum + (m.responseTime || 0),
    0
  );
  const totalAnswerDuration = userAnswers.reduce(
    (sum, m) => sum + (m.answerDuration || 0),
    0
  );

  const averageResponseTime =
    userAnswers.length > 0 ? totalResponseTime / userAnswers.length : 0;
  const averageAnswerDuration =
    userAnswers.length > 0 ? totalAnswerDuration / userAnswers.length : 0;

  const processedReport: InterviewReport = {
    overallScore: resp.overallScore,
    oneLineSummary: resp.oneLineSummary,
    strengths: resp.strengths,
    areasForImprovement: resp.areasForImprovement,
    averageResponseTime: parseFloat(averageResponseTime.toFixed(2)),
    averageAnswerDuration: parseFloat(averageAnswerDuration.toFixed(2)),
    totalFillerWords,
    questionAnalysis: processQuestionAnalysis(
      resp.questionAnalysis,
      chatHistory
    ),
  };

  return processedReport;
}

function processQuestionAnalysis(
  analysisData: any,
  chatHistory: ChatMessage[]
): InterviewReport["questionAnalysis"] {
  if (!Array.isArray(analysisData)) {
    return [];
  }

  return analysisData.map((qa: any) => {
    const originalMessage = chatHistory.find(
      (m) => m.from === "user" && m.message === qa.answer
    );
    const avatarQuestion = chatHistory.find(
      (m) =>
        m.from === "avatar" &&
        m.type === "question" &&
        m.message === qa.question
    );

    return {
      questionId: avatarQuestion?.questionId,
      question: qa.question,
      category: qa.category,
      answer: qa.answer,
      score: qa.score,
      feedback: qa.feedback,
      improvements: qa.improvements,
      responseTime: originalMessage?.responseTime,
      answerDuration: originalMessage?.answerDuration,
      fillerWordCount: originalMessage?.fillerWordCount,
    };
  });
}
