import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export default function LandingCTA() {
  return (
    <section className="py-20 bg-gradient-to-r from-emerald-600 via-teal-600 to-green-600">
      <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
          Ready to Land Your Dream Job?
        </h2>
        <p className="text-xl text-emerald-100 mb-8 max-w-2xl mx-auto leading-relaxed">
          Join thousands of successful candidates who used Preparify to ace
          their Big 4 interviews
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            variant="outline"
            size="lg"
            className="border-2 border-white text-primary hover:bg-white hover:text-primary/70"
          >
            <Link href="#firms">Choose Your Firm</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
