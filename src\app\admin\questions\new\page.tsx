"use client";

import type React from "react";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowLeft, Save, Loader2 } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

const categories = [
  { value: "behavioral", label: "Behavioral" },
  { value: "technical", label: "Technical" },
  { value: "case-based", label: "Case-based" },
];

const MAJOR_FIRMS_DATA = [
  { value: "deloitte", label: "Deloitte" },
  { value: "pwc", label: "PwC" },
  { value: "ey", label: "EY" },
  { value: "kpmg", label: "KPMG" },
];

const difficulties = [
  { value: "easy", label: "Easy" },
  { value: "medium", label: "Medium" },
  { value: "hard", label: "Hard" },
];

export default function NewQuestionPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    questionText: "", // Renamed from 'question' to match schema
    category: "",
    difficulty: "",
    firmName: "", // Changed from 'firms' (array) to 'firmName' (string)
    // tags, sampleAnswer, tips, status are not directly in schema, will omit for now
  });

  // Fetch firms dynamically
  const [firms, setFirms] = useState<{ value: string; label: string }[]>([]);
  useEffect(() => {
    const fetchFirms = async () => {
      try {
        const response = await fetch("/api/admin/firms");
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setFirms(
          data.map((firm: { id: string; name: string }) => ({
            value: firm.name.toLowerCase(),
            label: firm.name,
          }))
        );
      } catch (err: any) {
        console.error("Failed to fetch firms:", err);
        toast.error(err.message || "Failed to load firms data.");
      }
    };
    fetchFirms();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Validate required fields
      if (
        !formData.questionText ||
        !formData.category ||
        !formData.difficulty ||
        !formData.firmName
      ) {
        toast.error("Please fill in all required fields");
        setIsLoading(false);
        return;
      }

      const response = await fetch("/api/admin/questions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          questionText: formData.questionText,
          category: formData.category,
          firmName: formData.firmName,
          difficulty: formData.difficulty,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create question");
      }

      toast.success("Question created successfully!");
      router.push("/admin/questions"); // Redirect to the main admin page
    } catch (err: any) {
      console.error("Failed to create question:", err);
      toast.error(
        err.message || "Failed to create question. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Removed handleSaveAsDraft and handlePublish as status is not in schema

  return (
    <div className="px-6 py-16 max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Add New Question</h1>
          <p className="text-muted-foreground">
            Create a new interview question for Preparify
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Enter the core details of the interview question
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="question">Question *</Label>
              <Textarea
                id="questionText"
                placeholder="Enter the interview question..."
                value={formData.questionText}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    questionText: e.target.value,
                  }))
                }
                className="min-h-[100px]"
                required
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, category: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="difficulty">Difficulty *</Label>
                <Select
                  value={formData.difficulty}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, difficulty: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select difficulty" />
                  </SelectTrigger>
                  <SelectContent>
                    {difficulties.map((difficulty) => (
                      <SelectItem
                        key={difficulty.value}
                        value={difficulty.value}
                      >
                        {difficulty.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="firmName">Associated Firm *</Label>
              <Select
                value={formData.firmName}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, firmName: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select firm" />
                </SelectTrigger>
                <SelectContent>
                  {firms.map((firm) => (
                    <SelectItem key={firm.value} value={firm.label}>
                      {firm.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <div className="flex items-center justify-end gap-4">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Adding Question...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Add Question
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
