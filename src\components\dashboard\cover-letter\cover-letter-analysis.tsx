"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>ircle,
  FileText,
  TrendingUp,
  AlertTriangle,
  RefreshCw,
  Download,
  Star,
  MessageSquare,
  Target,
  FileEdit,
  ListChecks,
  Quote,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { CoverLetterAnalysisResult as CoverLetterAnalysisResultType } from "@/app/dashboard/cover-letter/actions";

// The type is now imported from actions.ts to ensure consistency.
export type CoverLetterAnalysisResult = CoverLetterAnalysisResultType;

interface CoverLetterAnalysisProps {
  result: CoverLetterAnalysisResult;
  onReset: () => void;
}

export function CoverLetterAnalysis({ result, onReset }: CoverLetterAnalysisProps) {
  const [activeTab, setActiveTab] = useState<"overview" | "line-by-line" | "actions" | "structure">("overview");

  const getRatingColor = (rating: number) => {
    if (rating >= 80) return "bg-green-100 text-green-800";
    if (rating >= 60) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const getRatingIcon = (rating: number) => {
    if (rating >= 80) return <CheckCircle className="w-6 h-6" />;
    if (rating >= 60) return <Star className="w-6 h-6" />;
    return <AlertTriangle className="w-6 h-6" />;
  };

  const getRatingText = (rating: number) => {
    if (rating >= 90) return "Excellent";
    if (rating >= 80) return "Very Good";
    if (rating >= 70) return "Good";
    if (rating >= 60) return "Fair";
    return "Needs Improvement";
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800 border-red-200";
      case "High":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "Medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Low":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getToneColor = (tone: string) => {
    switch (tone) {
      case "Professional":
        return "text-green-600";
      case "Casual":
        return "text-blue-600";
      case "Mixed":
        return "text-yellow-600";
      default:
        return "text-gray-600";
    }
  };

  const downloadReport = () => {
    const reportContent = generateReportText(result);
    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cover-letter-analysis-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const generateReportText = (analysis: CoverLetterAnalysisResult) => {
    const date = new Date().toLocaleDateString();
    return `
PROFESSIONAL COVER LETTER ANALYSIS REPORT
Generated on: ${date}

==========================================
OVERALL ASSESSMENT
==========================================
Overall Score: ${analysis.rating}/100 (${getRatingText(analysis.rating)})
Word Count: ${analysis.wordCount} words
Tone: ${analysis.tone}

==========================================
STRUCTURE ANALYSIS
==========================================
Opening Paragraph: ${analysis.structure.hasOpening ? "✓ Present" : "✗ Missing"}
Body Content: ${analysis.structure.hasBody ? "✓ Present" : "✗ Missing"}
Closing Paragraph: ${analysis.structure.hasClosing ? "✓ Present" : "✗ Missing"}
Call to Action: ${analysis.structure.hasCallToAction ? "✓ Present" : "✗ Missing"}

==========================================
GENERAL FEEDBACK
==========================================
${analysis.generalFeedback}

==========================================
KEY STRENGTHS
==========================================
${analysis.strengths.map((strength, index) => `${index + 1}. ${strength}`).join('\n')}

==========================================
KEY WEAKNESSES
==========================================
${analysis.weaknesses.map((weakness, index) => `${index + 1}. ${weakness}`).join('\n')}

==========================================
LINE-BY-LINE SUGGESTIONS
==========================================
${analysis.lineByLineSuggestions.map((item, index) => `
${index + 1}. ORIGINAL LINE:
   "${item.line}"
   
   ISSUE: ${item.issue}
   
   SUGGESTION:
   "${item.suggestion}"
`).join('\n\n')}

==========================================
RECOMMENDED ACTIONS
==========================================
${analysis.recommendedActions.map((action, index) => `
${index + 1}. ACTION: ${action.action} (${action.priority} Priority)
   IMPACT: ${action.expectedImpact}
`).join('\n')}

==========================================
This analysis was generated by Preparify's AI-powered cover letter review system.
    `.trim();
  };

  return (
    <div className="p-8 md:p-12">
      <div className="max-w-5xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            Cover Letter Analysis Results
          </h2>
        </div>

        {/* Rating Card */}
        <div className="mb-8 p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="md:col-span-1">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Overall Score</h3>
              <div className="flex items-center space-x-3">
                <div className={`p-3 rounded-full ${getRatingColor(result.rating)}`}>
                  {getRatingIcon(result.rating)}
                </div>
                <div>
                  <p className="text-3xl font-bold text-gray-900">
                    {result.rating}
                    <span className="text-lg text-gray-500 ml-1">/ 100</span>
                  </p>
                  <p className={`text-sm font-medium ${getRatingColor(result.rating).split(' ')[0]}`}>
                    {getRatingText(result.rating)}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="md:col-span-1">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Word Count</h3>
              <p className="text-2xl font-bold text-gray-900">{result.wordCount}</p>
              <p className="text-sm text-gray-600">
                {result.wordCount >= 250 && result.wordCount <= 400 ? "Optimal length" : 
                 result.wordCount < 250 ? "Too short" : "Too long"}
              </p>
            </div>
            
            <div className="md:col-span-1">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Tone</h3>
              <p className={`text-lg font-semibold ${getToneColor(result.tone)}`}>{result.tone}</p>
              <p className="text-sm text-gray-600">Communication Style</p>
            </div>
            
            <div className="md:col-span-1">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Structure</h3>
              <div className="flex space-x-1">
                {Object.values(result.structure).map((present, index) => (
                  <div
                    key={index}
                    className={`w-3 h-3 rounded-full ${present ? 'bg-green-500' : 'bg-gray-300'}`}
                  />
                ))}
              </div>
              <p className="text-sm text-gray-600">
                {Object.values(result.structure).filter(Boolean).length}/4 elements
              </p>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', label: 'Overview', icon: FileText },
                { id: 'line-by-line', label: 'Line-by-Line', icon: FileEdit },
                { id: 'actions', label: 'Actions', icon: ListChecks },
                { id: 'structure', label: 'Structure', icon: CheckCircle },
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {/* Overview Tab */}
          {activeTab === "overview" && (
            <div className="space-y-6">
              {/* General Feedback */}
              <div className="bg-white border border-gray-200 rounded-xl p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">General Assessment</h3>
                <p className="text-gray-700 leading-relaxed">{result.generalFeedback}</p>
              </div>

              {/* Strengths and Weaknesses */}
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                  <h3 className="text-lg font-bold text-green-900 mb-4 flex items-center">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    Strengths
                  </h3>
                  <ul className="space-y-2">
                    {result.strengths.map((strength, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                        <p className="text-green-800">{strength}</p>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="bg-orange-50 border border-orange-200 rounded-xl p-6">
                  <h3 className="text-lg font-bold text-orange-900 mb-4 flex items-center">
                    <AlertTriangle className="w-5 h-5 mr-2" />
                    Weaknesses
                  </h3>
                  <ul className="space-y-2">
                    {result.weaknesses.map((weakness, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                        <p className="text-orange-800">{weakness}</p>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Line-by-Line Tab */}
          {activeTab === "line-by-line" && (
            <div className="space-y-4">
              {result.lineByLineSuggestions.map((item, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-xl p-6">
                  <div className="mb-4">
                    <p className="text-sm font-medium text-gray-500 mb-2">Original Line</p>
                    <blockquote className="border-l-4 border-gray-300 pl-4 py-2 bg-gray-50">
                      <p className="text-gray-700 italic">"{item.line}"</p>
                    </blockquote>
                  </div>
                  <div className="mb-4">
                    <p className="text-sm font-medium text-red-600 mb-2">Issue</p>
                    <p className="text-gray-800">{item.issue}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-green-600 mb-2">Suggestion</p>
                    <blockquote className="border-l-4 border-green-300 pl-4 py-2 bg-green-50">
                      <p className="text-green-800 italic">"{item.suggestion}"</p>
                    </blockquote>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Recommended Actions Tab */}
          {activeTab === "actions" && (
            <div className="space-y-4">
              {result.recommendedActions.map((item, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-xl p-6">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-bold text-gray-900 flex-grow pr-4">{item.action}</h3>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium border whitespace-nowrap ${getPriorityColor(item.priority)}`}>
                      {item.priority} Priority
                    </span>
                  </div>
                  <p className="text-sm font-medium text-gray-500 mb-1">Expected Impact</p>
                  <p className="text-gray-700">{item.expectedImpact}</p>
                </div>
              ))}
            </div>
          )}

          {/* Structure Tab */}
          {activeTab === "structure" && (
            <div className="bg-white border border-gray-200 rounded-xl p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-6">Structure Analysis</h3>
              <div className="grid gap-4">
                {[
                  { key: 'hasOpening', label: 'Opening Paragraph', description: 'Clear introduction with position reference' },
                  { key: 'hasBody', label: 'Body Content', description: 'Substantial content with examples and achievements' },
                  { key: 'hasClosing', label: 'Closing Paragraph', description: 'Professional conclusion' },
                  { key: 'hasCallToAction', label: 'Call to Action', description: 'Clear next steps or interview request' },
                ].map((item) => {
                  const isPresent = result.structure[item.key as keyof typeof result.structure];
                  return (
                    <div key={item.key} className={`p-4 rounded-lg border ${isPresent ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                      <div className="flex items-center space-x-3">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${isPresent ? 'bg-green-500' : 'bg-red-500'}`}>
                          {isPresent ? (
                            <CheckCircle className="w-4 h-4 text-white" />
                          ) : (
                            <AlertTriangle className="w-4 h-4 text-white" />
                          )}
                        </div>
                        <div>
                          <h4 className={`font-medium ${isPresent ? 'text-green-900' : 'text-red-900'}`}>
                            {item.label}
                          </h4>
                          <p className={`text-sm ${isPresent ? 'text-green-700' : 'text-red-700'}`}>
                            {item.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="mt-8 pt-6 border-t border-gray-200 flex flex-col sm:flex-row gap-3">
          <Button
            onClick={downloadReport}
            className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
          >
            <Download className="w-4 h-4 mr-2" />
            Download Report
          </Button>
          
          <Button
            onClick={onReset}
            className="flex items-center justify-center px-4 py-2 bg-white text-gray-700 text-sm font-medium rounded-md border border-gray-300 hover:bg-gray-50"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Analyze Another
          </Button>
        </div>
      </div>
    </div>
  );
}
