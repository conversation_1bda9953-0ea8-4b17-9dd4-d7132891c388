// Industry-specific analysis and standards for resume evaluation

export interface IndustryStandards {
  name: string;
  keySkills: string[];
  certifications: string[];
  experienceKeywords: string[];
  achievementMetrics: string[];
  careerProgression: string[];
  commonRoles: string[];
  technicalSkills: string[];
  softSkills: string[];
  industrySpecificAdvice: string[];
}

export const industryStandards: Record<string, IndustryStandards> = {
  "big4-auditing": {
    name: "Big 4 Auditing & Professional Services",
    keySkills: [
      "Financial Statement Analysis",
      "GAAP/IFRS",
      "Risk Assessment",
      "Internal Controls",
      "SOX Compliance",
      "Audit Planning",
      "Client Management",
      "Team Leadership",
      "Process Improvement",
      "Regulatory Compliance"
    ],
    certifications: [
      "CPA (Certified Public Accountant)",
      "CIA (Certified Internal Auditor)",
      "CISA (Certified Information Systems Auditor)",
      "CFA (Chartered Financial Analyst)",
      "CA (Chartered Accountant)",
      "ACCA (Association of Chartered Certified Accountants)"
    ],
    experienceKeywords: [
      "audit engagement",
      "client relationship",
      "financial reporting",
      "risk management",
      "compliance testing",
      "substantive procedures",
      "walkthrough",
      "materiality assessment",
      "sampling methodology",
      "workpaper preparation",
      "senior review",
      "partner interaction",
      "SEC reporting",
      "PCAOB standards"
    ],
    achievementMetrics: [
      "audit hours efficiency",
      "client satisfaction scores",
      "team size managed",
      "revenue audited",
      "findings identified",
      "process improvements",
      "cost savings",
      "training hours delivered",
      "utilization rates",
      "business development"
    ],
    careerProgression: [
      "Staff Accountant",
      "Senior Associate",
      "Manager",
      "Senior Manager",
      "Director",
      "Partner"
    ],
    commonRoles: [
      "Audit Associate",
      "Senior Auditor",
      "Audit Manager",
      "Risk Advisory Consultant",
      "Financial Advisory Consultant",
      "Tax Associate",
      "Forensic Accountant"
    ],
    technicalSkills: [
      "Excel (Advanced)",
      "SQL",
      "ACL",
      "IDEA",
      "Tableau",
      "Power BI",
      "SAP",
      "Oracle",
      "TeamMate",
      "Workiva",
      "CaseWare"
    ],
    softSkills: [
      "Client Communication",
      "Team Leadership",
      "Project Management",
      "Problem Solving",
      "Attention to Detail",
      "Time Management",
      "Adaptability",
      "Professional Skepticism",
      "Ethical Judgment",
      "Stakeholder Management"
    ],
    industrySpecificAdvice: [
      "Quantify audit scope with specific dollar amounts and entity sizes",
      "Highlight experience with SEC registrants vs. private companies",
      "Emphasize cross-functional collaboration with IT, Tax, and Advisory teams",
      "Include experience with complex accounting standards (ASC 606, 842, etc.)",
      "Demonstrate progression from staff-level testing to senior-level planning",
      "Show experience managing client relationships and difficult conversations",
      "Highlight any specialized industry experience (banking, healthcare, technology)",
      "Include metrics on team management and training responsibilities",
      "Emphasize business development and proposal writing experience for senior roles",
      "Show understanding of current regulatory environment and emerging standards"
    ]
  },
  
  "consulting": {
    name: "Management Consulting",
    keySkills: [
      "Strategic Planning",
      "Business Analysis",
      "Process Improvement",
      "Change Management",
      "Data Analysis",
      "Client Engagement",
      "Project Management",
      "Financial Modeling",
      "Market Research",
      "Stakeholder Management"
    ],
    certifications: [
      "PMP (Project Management Professional)",
      "Six Sigma",
      "Lean Certification",
      "Scrum Master",
      "Change Management Certification"
    ],
    experienceKeywords: [
      "client engagement",
      "strategic initiative",
      "process optimization",
      "business transformation",
      "stakeholder alignment",
      "executive presentation",
      "market analysis",
      "competitive intelligence",
      "operational efficiency",
      "digital transformation"
    ],
    achievementMetrics: [
      "cost savings delivered",
      "revenue increase",
      "efficiency improvements",
      "project timeline",
      "client satisfaction",
      "team size",
      "process cycle time reduction",
      "ROI generated"
    ],
    careerProgression: [
      "Analyst",
      "Associate",
      "Senior Associate",
      "Principal",
      "Partner"
    ],
    commonRoles: [
      "Business Analyst",
      "Management Consultant",
      "Strategy Consultant",
      "Operations Consultant",
      "Digital Transformation Consultant"
    ],
    technicalSkills: [
      "Excel (Advanced)",
      "PowerPoint",
      "SQL",
      "Python",
      "R",
      "Tableau",
      "Power BI",
      "Visio",
      "Project Management Tools"
    ],
    softSkills: [
      "Executive Communication",
      "Problem Solving",
      "Analytical Thinking",
      "Leadership",
      "Adaptability",
      "Client Relationship Management",
      "Presentation Skills",
      "Negotiation"
    ],
    industrySpecificAdvice: [
      "Quantify business impact with specific metrics and percentages",
      "Highlight experience across different industries and functional areas",
      "Emphasize client-facing experience and relationship building",
      "Show progression from analysis to strategy development",
      "Include experience with C-suite stakeholders",
      "Demonstrate ability to manage complex, multi-workstream projects",
      "Highlight any thought leadership or publication experience",
      "Show experience with digital tools and emerging technologies"
    ]
  },

  "finance": {
    name: "Corporate Finance & Investment Banking",
    keySkills: [
      "Financial Modeling",
      "Valuation Analysis",
      "M&A Analysis",
      "Capital Markets",
      "Risk Management",
      "Financial Planning",
      "Investment Analysis",
      "Due Diligence",
      "Regulatory Compliance",
      "Portfolio Management"
    ],
    certifications: [
      "CFA (Chartered Financial Analyst)",
      "FRM (Financial Risk Manager)",
      "CPA (Certified Public Accountant)",
      "Series 7, 63, 66",
      "CAIA (Chartered Alternative Investment Analyst)"
    ],
    experienceKeywords: [
      "financial modeling",
      "DCF analysis",
      "comparable company analysis",
      "merger model",
      "LBO model",
      "pitch book",
      "due diligence",
      "capital raising",
      "IPO process",
      "credit analysis"
    ],
    achievementMetrics: [
      "deal value",
      "transaction volume",
      "portfolio performance",
      "cost of capital reduction",
      "revenue generated",
      "assets under management",
      "client acquisition",
      "risk reduction"
    ],
    careerProgression: [
      "Analyst",
      "Associate",
      "Vice President",
      "Director",
      "Managing Director"
    ],
    commonRoles: [
      "Investment Banking Analyst",
      "Financial Analyst",
      "Portfolio Manager",
      "Risk Analyst",
      "Corporate Development Associate"
    ],
    technicalSkills: [
      "Excel (Advanced)",
      "Bloomberg Terminal",
      "FactSet",
      "Capital IQ",
      "Refinitiv",
      "Python",
      "R",
      "SQL",
      "VBA"
    ],
    softSkills: [
      "Analytical Thinking",
      "Attention to Detail",
      "Client Relationship Management",
      "Pressure Management",
      "Communication",
      "Team Collaboration",
      "Time Management"
    ],
    industrySpecificAdvice: [
      "Quantify deal experience with transaction values and types",
      "Highlight experience with specific financial instruments and markets",
      "Show progression from modeling to client interaction",
      "Include experience with regulatory requirements and compliance",
      "Demonstrate understanding of market dynamics and economic factors",
      "Highlight any sector specialization or industry expertise"
    ]
  }
};

export function detectIndustry(resumeContent: string): string {
  const content = resumeContent.toLowerCase();
  
  // Big 4 Auditing keywords
  const auditingKeywords = ["audit", "gaap", "ifrs", "sox", "pcaob", "sec", "cpa", "financial statement", "internal control", "risk assessment"];
  const auditingScore = auditingKeywords.filter(keyword => content.includes(keyword)).length;
  
  // Consulting keywords
  const consultingKeywords = ["consulting", "strategy", "process improvement", "change management", "business transformation", "stakeholder"];
  const consultingScore = consultingKeywords.filter(keyword => content.includes(keyword)).length;
  
  // Finance keywords
  const financeKeywords = ["investment banking", "financial modeling", "valuation", "m&a", "capital markets", "portfolio", "bloomberg"];
  const financeScore = financeKeywords.filter(keyword => content.includes(keyword)).length;
  
  // Determine industry based on highest score
  if (auditingScore >= consultingScore && auditingScore >= financeScore && auditingScore > 0) {
    return "big4-auditing";
  } else if (consultingScore >= financeScore && consultingScore > 0) {
    return "consulting";
  } else if (financeScore > 0) {
    return "finance";
  }
  
  return "general";
}

export function getIndustrySpecificPrompt(industry: string): string {
  const standards = industryStandards[industry];
  if (!standards) return "";
  
  return `
INDUSTRY-SPECIFIC ANALYSIS FOR ${standards.name.toUpperCase()}:

Key Skills to Look For:
${standards.keySkills.map(skill => `- ${skill}`).join('\n')}

Important Certifications:
${standards.certifications.map(cert => `- ${cert}`).join('\n')}

Experience Keywords:
${standards.experienceKeywords.map(keyword => `- ${keyword}`).join('\n')}

Achievement Metrics:
${standards.achievementMetrics.map(metric => `- ${metric}`).join('\n')}

Career Progression:
${standards.careerProgression.map(level => `- ${level}`).join('\n')}

Technical Skills:
${standards.technicalSkills.map(skill => `- ${skill}`).join('\n')}

Industry-Specific Advice:
${standards.industrySpecificAdvice.map(advice => `- ${advice}`).join('\n')}

When analyzing this resume, pay special attention to:
1. Presence and depth of industry-specific keywords
2. Appropriate career progression for the industry
3. Relevant certifications and technical skills
4. Quantified achievements using industry-standard metrics
5. Evidence of industry best practices and standards knowledge
`;
}
