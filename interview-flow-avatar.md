- the questions and whole interview wshould happen dynamically powered by llama-4-mavrick model from groq. 

- the llm should get conetxt of the questions there are stored in the db (uploaded by the admin), 

- the interview will be more natural, not speifc ammount of back and fourth, but should have exactly N number of questions for each of the 3 category, (for now N = 1)

- after each message from the avatar, it should start recording audio of the user, and translate the audio using whisper model (from groq also),  so then we would have a text of what the user said

- then we would pass the tetx to llm again nd it iwll say another message, back and foruth so on

- llm should responde in json format, like {message: , type: general | question | end, then a category field (null by default, only pass if the type is question) should mention what category the quesiton falls into}

(llm shoudl send end type when the interviw is over menainf necessary stuf is done, this will end the interview)

- we will store all these history chat in the client  side in localost for now, (can updae to redis storage in future, for now localhost is fine) meaning everytime the client side should send the full interview chat history of that current interveiw (should store in local host as list of interviews with the id)

- then after the interviw ends, the whole chat hisotry will for the lasts time get sent to a llm with a different instruciton, whose job would be to make a detailse report of the interview, like what quesiotns oere naswered right, suggesitons & things to improve. (in json format also)

- then this rpeort will be shown in the /dashboard/interview/report/interview_id (this interveiw Id would be fro mthe local host, menaing which interviw fro mthe local host should a reprot get generated for )