"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Users,
  FileQuestion,
  Plus,
  Edit,
  Trash2,
  Filter,
  Search,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

interface Question {
  id: string;
  questionText: string;
  category: string;
  firmId: string;
  difficulty: string;
  createdAt: string;
  updatedAt: string;
}

interface Firm {
  id: string;
  name: string;
}

interface Stats {
  totalQuestions: number;
  totalFirms: number;
}

const categories = ["All", "Behavioral", "Technical", "Case-based"];
const difficulties = ["All", "Easy", "Medium", "Hard"];

export default function AdminDashboard() {
  const [stats, setStats] = useState<Stats | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [firmsData, setFirmsData] = useState<Firm[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [selectedFirm, setSelectedFirm] = useState("All");
  const [selectedDifficulty, setSelectedDifficulty] = useState("All");
  const [currentPage, setCurrentPage] = useState(1);
  const [isFirmsDialogOpen, setIsFirmsDialogOpen] = useState(false);
  const itemsPerPage = 10;

  const fetchFirms = async () => {
    try {
      const response = await fetch("/api/admin/firms");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data: Firm[] = await response.json();
      setFirmsData(data);
    } catch (err) {
      console.error("Failed to fetch firms data:", err);
      setError("Failed to load firms data.");
    }
  };

  const fetchQuestions = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append("search", searchTerm);
      if (selectedCategory !== "All")
        params.append("category", selectedCategory);
      const firmId =
        selectedFirm !== "All"
          ? firmsData.find((f) => f.name === selectedFirm)?.id
          : undefined;
      if (firmId) params.append("firmId", firmId);
      if (selectedDifficulty !== "All")
        params.append("difficulty", selectedDifficulty);

      const response = await fetch(`/api/admin/questions?${params.toString()}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data: Question[] = await response.json();

      const totalQuestions = data.length;
      const totalFirms = new Set(data.map((q) => q.firmId)).size;
      setStats({ totalQuestions, totalFirms });

      setQuestions(data);
      setCurrentPage(1);
    } catch (err) {
      console.error("Failed to fetch questions:", err);
      setError("Failed to load questions. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [
    searchTerm,
    selectedCategory,
    selectedFirm,
    selectedDifficulty,
    firmsData,
  ]);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        await fetchFirms();
      } catch (err) {
        console.error("Initial data fetch failed:", err);
        setError("Failed to load initial dashboard data.");
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    if (firmsData.length > 0) {
      fetchQuestions();
    }
  }, [fetchQuestions, firmsData.length]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case "easy":
        return "bg-green-100 text-green-800 border-green-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "hard":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this question?")) {
      return;
    }
    try {
      const response = await fetch(`/api/admin/questions?id=${id}`, {
        method: "DELETE",
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete question");
      }
      toast.success("Question deleted successfully!");
      fetchQuestions();
    } catch (err: any) {
      console.error("Failed to delete question:", err);
      toast.error(
        err.message || "Failed to delete question. Please try again."
      );
      setError(err.message || "Failed to delete question. Please try again.");
    }
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedCategory("All");
    setSelectedFirm("All");
    setSelectedDifficulty("All");
  };

  const handleDeleteFirm = async (firmId: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this firm? This action cannot be undone."
      )
    ) {
      return;
    }
    try {
      const response = await fetch(`/api/admin/firms?id=${firmId}`, {
        method: "DELETE",
      });
      if (!response.ok) {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to delete firm");
      } else {
        toast.success("Firm deleted successfully!");
        fetchFirms(); // Re-fetch firms to update the list
        fetchQuestions(); // Re-fetch questions as firm data might affect filters
      }
    } catch (err: any) {
      console.error("Failed to delete firm:", err);
      toast.error(err.message || "Failed to delete firm. Please try again.");
      setError(err.message || "Failed to delete firm. Please try again.");
    }
  };

  const totalPages = Math.ceil(questions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedQuestions = questions.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(totalPages, page)));
  };

  if (isLoading && questions.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50/50 p-4 sm:p-6 lg:p-8">
        <div className="mx-auto max-w-7xl">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="h-40 bg-gray-200 rounded"></div>
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className="mx-auto max-w-7xl p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="space-y-1">
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">
              Questions Dashboard
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage your Preparify-Prep content
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
            <Button asChild className="w-full sm:w-auto">
              <Link
                href="/admin/questions/new"
                className="flex items-center justify-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Question
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full sm:w-auto">
              <Link
                href="/admin/firms/new"
                className="flex items-center justify-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Firm
              </Link>
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          <Card className="bg-white shadow-sm border-0 ring-1 ring-gray-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-medium text-gray-600">
                Total Questions
              </CardTitle>
              <div className="p-2 bg-blue-50 rounded-lg">
                <FileQuestion className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl font-bold text-gray-900">
                {stats?.totalQuestions || 0}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Total in system
              </p>
            </CardContent>
          </Card>

          <Dialog open={isFirmsDialogOpen} onOpenChange={setIsFirmsDialogOpen}>
            <DialogTrigger asChild>
              <Card className="bg-white shadow-sm border-0 ring-1 ring-gray-200 cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">
                    Firms
                  </CardTitle>
                  <div className="p-2 bg-green-50 rounded-lg">
                    <Users className="h-4 w-4 text-green-600" />
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="text-2xl font-bold text-gray-900">
                    {firmsData.length}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Different firms created
                  </p>
                </CardContent>
              </Card>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Registered Firms</DialogTitle>
                <DialogDescription>
                  A list of all firms currently registered in the system.
                </DialogDescription>
              </DialogHeader>
              <ScrollArea className="h-72 w-full rounded-md border">
                <div className="p-4">
                  {firmsData.length > 0 ? (
                    <ul className="space-y-2">
                      {firmsData.map((firm) => (
                        <li
                          key={firm.id}
                          className="flex items-center justify-between py-2 px-3 rounded-md hover:bg-gray-50 transition-colors"
                        >
                          <span className="font-medium text-gray-800">
                            {firm.name}
                          </span>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              ID: {firm.id.substring(0, 8)}...
                            </Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                              onClick={() => handleDeleteFirm(firm.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-center text-muted-foreground">
                      No firms registered yet.
                    </p>
                  )}
                </div>
              </ScrollArea>
            </DialogContent>
          </Dialog>

          <Card className="bg-white shadow-sm border-0 ring-1 ring-gray-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-medium text-gray-600">
                Categories
              </CardTitle>
              <div className="p-2 bg-purple-50 rounded-lg">
                <Filter className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl font-bold text-gray-900">
                {categories.length - 1}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Question categories
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0 ring-1 ring-gray-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-medium text-gray-600">
                Filtered Results
              </CardTitle>
              <div className="p-2 bg-orange-50 rounded-lg">
                <Search className="h-4 w-4 text-orange-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl font-bold text-gray-900">
                {questions.length}
              </div>
              <p className="text-xs text-muted-foreground mt-1">Current view</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="bg-white shadow-sm border-0 ring-1 ring-gray-200">
          <CardHeader className="pb-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Filter className="h-5 w-5" />
                Filters & Search
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                className="w-full sm:w-auto"
              >
                Clear Filters
              </Button>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search questions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-10"
                />
              </div>

              {/* Filter Selects */}
              <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Category
                  </label>
                  <Select
                    value={selectedCategory}
                    onValueChange={setSelectedCategory}
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Firm
                  </label>
                  <Select value={selectedFirm} onValueChange={setSelectedFirm}>
                    <SelectTrigger className="h-10">
                      <SelectValue placeholder="Select firm" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="All">All Firms</SelectItem>
                      {firmsData.map((firm) => (
                        <SelectItem key={firm.id} value={firm.name}>
                          {firm.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Difficulty
                  </label>
                  <Select
                    value={selectedDifficulty}
                    onValueChange={setSelectedDifficulty}
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue placeholder="Select difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      {difficulties.map((difficulty) => (
                        <SelectItem key={difficulty} value={difficulty}>
                          {difficulty}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Questions Table */}
        <Card className="bg-white shadow-sm border-0 ring-1 ring-gray-200">
          <CardHeader className="pb-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <CardTitle className="text-lg">
                  Questions ({questions.length})
                </CardTitle>
                <CardDescription className="mt-1">
                  {questions.length > 0 ? (
                    <>
                      Showing {startIndex + 1}-
                      {Math.min(startIndex + itemsPerPage, questions.length)} of{" "}
                      {questions.length} questions
                    </>
                  ) : (
                    "No questions found"
                  )}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            {/* Mobile Cards View */}
            <div className="block sm:hidden space-y-4">
              {paginatedQuestions.map((question) => (
                <Card key={question.id} className="p-4 border border-gray-200">
                  <div className="space-y-3">
                    <p className="font-medium text-sm leading-relaxed">
                      {question.questionText}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="outline" className="text-xs">
                        {question.category}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {firmsData.find((f) => f.id === question.firmId)
                          ?.name || "N/A"}
                      </Badge>
                      <Badge
                        className={`text-xs ${getDifficultyColor(
                          question.difficulty
                        )}`}
                      >
                        {question.difficulty}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between pt-2">
                      <span className="text-xs text-muted-foreground">
                        {new Date(question.createdAt).toLocaleDateString()}
                      </span>
                      <div className="flex items-center gap-1">
                        <Link href={`/admin/questions/edit/${question.id}`}>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                        </Link>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                          onClick={() => handleDelete(question.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Desktop Table View */}
            <div className="hidden sm:block overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="border-gray-200">
                    <TableHead className="font-semibold text-gray-900">
                      Question
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900">
                      Category
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900">
                      Firm
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900">
                      Difficulty
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900">
                      Created
                    </TableHead>
                    <TableHead className="text-right font-semibold text-gray-900">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedQuestions.map((question) => (
                    <TableRow
                      key={question.id}
                      className="border-gray-100 hover:bg-gray-50/50"
                    >
                      <TableCell className="max-w-md py-4">
                        <p className="font-medium line-clamp-2 text-sm leading-relaxed">
                          {question.questionText}
                        </p>
                      </TableCell>
                      <TableCell className="py-4">
                        <Badge variant="outline" className="font-medium">
                          {question.category}
                        </Badge>
                      </TableCell>
                      <TableCell className="py-4">
                        <Badge variant="outline" className="font-medium">
                          {firmsData.find((f) => f.id === question.firmId)
                            ?.name || "N/A"}
                        </Badge>
                      </TableCell>
                      <TableCell className="py-4">
                        <Badge
                          className={`font-medium ${getDifficultyColor(
                            question.difficulty
                          )}`}
                        >
                          {question.difficulty}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-muted-foreground py-4 text-sm">
                        {new Date(question.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-right py-4">
                        <div className="flex items-center justify-end gap-1">
                          <Link href={`/admin/questions/edit/${question.id}`}>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleDelete(question.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {questions.length === 0 && !isLoading && (
              <div className="text-center py-12">
                <FileQuestion className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No questions found
                </h3>
                <p className="text-gray-500 mb-6">
                  Try adjusting your filters or search terms, or create a new
                  question.
                </p>
                <Button asChild>
                  <Link href="/admin/questions/new">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Question
                  </Link>
                </Button>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 pt-6 border-t border-gray-200">
                <div className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages}
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => goToPage(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="h-9"
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Previous
                  </Button>

                  {/* Page Numbers */}
                  <div className="hidden sm:flex items-center gap-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          variant={
                            currentPage === pageNum ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() => goToPage(pageNum)}
                          className="h-9 w-9 p-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => goToPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="h-9"
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {error && (
          <Card className="bg-red-50 border-red-200">
            <CardContent className="pt-6">
              <p className="text-red-800 text-sm">{error}</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
