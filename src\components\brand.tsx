import { BookMarked } from "lucide-react";

export const NameWithLogo = () => {
  return (
    <div className="flex gap-2 items-center">
      <BookMarked className="h-8 w-8 text-emerald-600" />

      <span className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
        Preparify
      </span>
    </div>
  );
};

export const NameWithLogoAdmin = () => {
  return (
    <div className="flex gap-2 items-center">
      <BookMarked className="h-8 w-8 text-emerald-600" />

      <span className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
        Preparify Admin
      </span>
    </div>
  );
};
