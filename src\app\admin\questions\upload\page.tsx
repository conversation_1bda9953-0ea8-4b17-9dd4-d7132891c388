"use client"

import { useState } from "react"
import { useR<PERSON><PERSON> } from "next/navigation"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ArrowLeft, Upload, Loader2 } from "lucide-react"
import Link from "next/link"

export default function UploadQuestionsPage() {
  const router = useRouter()
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    setSelectedFile(file);
    setError(null); // Clear previous errors on new file selection
    setSuccessMessage(null); // Clear previous success messages
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setError("Please select a CSV file to upload.");
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    const formData = new FormData();
    formData.append("file", selectedFile);

    try {
      const response = await fetch("/api/admin/questions/upload", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to upload file");
      }

      const result = await response.json();
      setSuccessMessage(result.message || "File uploaded successfully!");
      setSelectedFile(null); // Clear selected file after successful upload
    } catch (err: any) {
      console.error("Error uploading file:", err);
      setError(err.message || "Failed to upload file. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/questions" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Questions
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Upload Questions (CSV)</h1>
          <p className="text-muted-foreground">Upload multiple questions using a CSV file</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Select CSV File</CardTitle>
          <CardDescription>Choose a CSV file with question data to upload.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="csvFile">CSV File</Label>
            <Input id="csvFile" type="file" accept=".csv" onChange={handleFileChange} />
          </div>

          {selectedFile && (
            <div className="text-sm text-muted-foreground">Selected file: {selectedFile.name}</div>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {successMessage && (
            <Alert variant="default" className="bg-green-100 text-green-700">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          <Button onClick={handleUpload} disabled={!selectedFile || isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Upload CSV
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}