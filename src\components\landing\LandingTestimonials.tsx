import { Star } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

interface Testimonial {
  name: string;
  role: string;
  content: string;
  rating: number;
}

const TESTIMONIALS: Testimonial[] = [
  {
    name: "<PERSON>",
    role: "Consultant at Deloitte",
    content:
      "Preparify helped me land my dream job at Deloitte. The AI feedback was incredibly detailed and helped me improve my case interview skills.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Senior Associate at PwC",
    content:
      "The firm-specific preparation was exactly what I needed. I felt confident walking into my PwC interviews.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Analyst at EY",
    content:
      "The behavioral interview practice was a game-changer. I knew exactly how to structure my responses using the STAR method.",
    rating: 5,
  },
];

export default function LandingTestimonials() {
  return (
    <section className="py-20 bg-gradient-to-br from-emerald-50 to-teal-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Success Stories
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Join thousands who landed their dream Big 4 jobs with Preparify
          </p>
        </div>
        <div className="grid md:grid-cols-3 gap-8">
          {TESTIMONIALS.map((testimonial, index) => (
            <Card
              key={index}
              className="bg-white shadow-md border border-emerald-100"
            >
              <CardContent className="p-6">
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-5 w-5 text-yellow-400 fill-current"
                    />
                  ))}
                </div>
                <CardDescription className="text-gray-600 mb-4 italic leading-relaxed">
                  &ldquo;{testimonial.content}&rdquo;
                </CardDescription>
                <div>
                  <CardTitle className="font-semibold text-gray-900">
                    {testimonial.name}
                  </CardTitle>
                  <p className="text-emerald-600 text-sm font-medium">
                    {testimonial.role}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}