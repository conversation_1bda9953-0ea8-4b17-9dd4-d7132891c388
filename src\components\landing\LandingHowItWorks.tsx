import { ArrowR<PERSON>, Check } from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON>Title,
  CardDescription,
} from "@/components/ui/card";

interface Step {
  step: string;
  title: string;
  desc: string;
}

const STEPS: Step[] = [
  {
    step: "1",
    title: "Choose Your Firm",
    desc: "Select from Deloitte, PwC, EY, or KPMG",
  },
  {
    step: "2",
    title: "Pick Interview Type",
    desc: "Behavioral, technical, or case-based questions",
  },
  {
    step: "3",
    title: "Practice with AI",
    desc: "Record your responses to realistic questions",
  },
  {
    step: "4",
    title: "Get Feedback",
    desc: "Receive detailed analysis and improvement tips",
  },
];

export default function LandingHowItWorks() {
  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-emerald-50/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            How It Works
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Get interview-ready in just 4 simple steps
          </p>
        </div>
        <div className="grid md:grid-cols-4 gap-8">
          {STEPS.map((item, index) => (
            <Card key={index} className="bg-white shadow-md">
              <CardContent className="text-center">
                <div className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold shadow-lg">
                  {item.step}
                </div>
                <CardTitle className="text-lg font-semibold text-gray-900 mb-2">
                  {item.title}
                </CardTitle>
                <CardDescription className="text-gray-600">
                  {item.desc}
                </CardDescription>
                {index < 3 ? (
                  <ArrowRight className="h-6 w-6 text-green-600 mx-auto mt-4" />
                ) : (
                  <Check className="h-6 w-6 text-green-600 mx-auto mt-4" />
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
