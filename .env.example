# Neon Database
DATABASE_URL="****************************************************"

# NextAuth.js Credentials
GOOGLE_CLIENT_ID="YOUR_GOOGLE_CLIENT_ID"
GOOGLE_CLIENT_SECRET="YOUR_GOOGLE_CLIENT_SECRET"

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3000"
# Generate a strong secret using: openssl rand -base64 32
NEXTAUTH_SECRET="YOUR_STRONG_RANDOM_SECRET"

# Email Configuration for Magic Links
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT="587"
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

OPENROUTER_API_KEY=""
GROQ_API_KEY=""

# Admin Credentials
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="password"

# HeyGen
HEYGEN_API_KEY=your_heygen_api_key_here
