import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import {
  interviewReports,
  questionResponses,
  interviewSessions,
} from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }


    const body = await request.json();
    const {
      sessionId,
      overallScore,
      oneLineSummary,
      strengths,
      areasForImprovement,
      averageResponseTime,
      averageAnswerDuration,
      totalFillerWords,
      questionAnalysis,
    } = body;

    if (!sessionId) {
      return NextResponse.json(
{ error: "Session ID is required" },
        { status: 400 });
    }

    // Verify the session belongs to the user
    const sessionExists = await db
      .select()
      .from(interviewSessions)
      .where(
        and(
          eq(interviewSessions.id, sessionId),
          eq(interviewSessions.userId, session.user.id)
        )
      )
      .limit(1);

    if (sessionExists.length === 0) {
      return NextResponse.json(
        { error: "Session not found or unauthorized" },
        { status: 404 }
      );
    }

    // Check if report already exists
    const existingReport = await db
      .select()
      .from(interviewReports)
      .where(eq(interviewReports.sessionId, sessionId))
      .limit(1);

    if (existingReport.length > 0) {
      return NextResponse.json(
        { error: "Report already exists for this session" },
        { status: 409 }
      );
    }

    // Create interview report
    const reportId = uuidv4();
    const newReport = {
      id: reportId,
      sessionId,
      overallScore,
      oneLineSummary,
      strengths,
      areasForImprovement,
      averageResponseTime,
      averageAnswerDuration,
      totalFillerWords,
    };

    await db.insert(interviewReports).values(newReport);

    // Create question responses
    if (questionAnalysis && Array.isArray(questionAnalysis)) {
      const questionResponsesData = questionAnalysis
        .filter((qa: any) => qa.questionId) // Only include responses with valid question IDs
        .map((qa: any) => ({
          id: uuidv4(),
          sessionId,
          questionId: qa.questionId,
          questionText: qa.question,
          userAnswer: qa.answer,
          score: qa.score,
          feedback: qa.feedback,
          improvements: qa.improvements || [],
          responseTime: qa.responseTime,
          answerDuration: qa.answerDuration,
          fillerWordCount: qa.fillerWordCount,
        }));

      if (questionResponsesData.length > 0) {
        await db.insert(questionResponses).values(questionResponsesData);
      } else {
        console.warn(
          "No question responses to insert - all responses filtered out due to missing questionId"
        );
      }
    } else {
      console.warn(
        "No questionAnalysis provided or not an array:",
        questionAnalysis
      );
    }

    // Update session status to completed
    await db
      .update(interviewSessions)
      .set({
        status: "completed",
        completedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(interviewSessions.id, sessionId));

    return NextResponse.json({
      reportId,
      message: "Interview report saved successfully",
    });
  } catch (error) {
    console.error("Error saving interview report:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get("sessionId");

    if (!sessionId) {
      return NextResponse.json(
        { error: "Session ID is required" },
        { status: 400 }
      );
    }

    // Get the report with question responses
    const report = await db
      .select()
      .from(interviewReports)
      .where(eq(interviewReports.sessionId, sessionId))
      .limit(1);

    if (report.length === 0) {
      return NextResponse.json({ error: "Report not found" }, { status: 404 });
    }

    const responses = await db
      .select()
      .from(questionResponses)
      .where(eq(questionResponses.sessionId, sessionId));

    const reportData = {
      ...report[0],
      questionAnalysis: responses.map((r) => ({
        question: r.questionText,
        category: "general", // You might want to add category to questionResponses table
        answer: r.userAnswer,
        score: r.score,
        feedback: r.feedback,
        improvements: r.improvements,
        responseTime: r.responseTime,
        answerDuration: r.answerDuration,
        fillerWordCount: r.fillerWordCount,
      })),
    };

    return NextResponse.json(reportData);
  } catch (error) {
    console.error("Error fetching interview report:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
