"use client";

import { useEffect, useState, useCallback } from "react";
import { useParams } from "next/navigation";
import { getInterview } from "@/utils/interviewStorage";
import {
  generateInterviewReport,
  type InterviewReport,
} from "@/lib/interviewLlm";
import { type ChatMessage } from "@/utils/interviewStorage";
import {
  AlertCircle,
  ArrowLeft,
  Bot,
  CheckCircle,
  FileWarning,
  RefreshCw,
  Star,
  TrendingUp,
  User,
} from "lucide-react";
import Link from "next/link";
import { toast } from "react-hot-toast";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

// Skeleton loading components
const ReportSkeleton = () => (
  <div className="space-y-6">
    <div className="flex items-center gap-4">
      <Skeleton className="w-12 h-12 rounded-full" />
      <div className="space-y-2">
        <Skeleton className="w-48 h-6" />
        <Skeleton className="w-32 h-4" />
      </div>
    </div>
    <Skeleton className="h-48 w-full" />
    <div className="grid md:grid-cols-2 gap-6">
      {[0, 1].map((i) => (
        <div key={i} className="space-y-4">
          <Skeleton className="w-32 h-6" />
          <Skeleton className="h-32 w-full" />
        </div>
      ))}
    </div>
    <Skeleton className="h-64 w-full" />
  </div>
);

export default function ReportPage() {
  const params = useParams();
  const interviewId = params.interviewId as string;
  const [report, setReport] = useState<InterviewReport | null>(null);
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [overallScore, setOverallScore] = useState<number | null>(null);

  const hasReport = !!report;
  const hasChatHistory = chatHistory.length > 0;

  useEffect(() => {
    const loadInterview = async () => {
      if (!interviewId) return;
      setLoading(true);

      try {
        // First try to fetch from database
        const reportResponse = await fetch(
          `/api/interview/report?sessionId=${interviewId}`
        );

        if (reportResponse.ok) {
          const reportData = await reportResponse.json();
          setReport(reportData);
          setOverallScore(reportData.overallScore);

          // We don't need chat history for database reports since we have the processed data
          setChatHistory([]);
          return;
        }

        // Fallback to localStorage for backward compatibility
        const history = getInterview(interviewId);
        if (!history || history.length === 0) {
          throw new Error(
            "Interview data not found. It might have been cleared from your browser's storage or the session ID is invalid."
          );
        }

        setChatHistory(history);

        try {
          const generatedReport: InterviewReport =
            await generateInterviewReport(history);
          setReport(generatedReport);

          // Calculate overall score as average of question scores
          if (generatedReport.questionAnalysis.length > 0) {
            const totalScore = generatedReport.questionAnalysis.reduce(
              (sum, qa) => sum + qa.score,
              0
            );
            const averageScore =
              totalScore / generatedReport.questionAnalysis.length;
            setOverallScore(Math.round(averageScore * 10) / 10); // Round to 1 decimal place
          } else {
            setOverallScore(0);
          }
        } catch (e) {
          console.error("Error generating report:", e);
          setError(
            "Failed to generate the report. The AI model may be temporarily unavailable."
          );
        }
      } catch (e) {
        console.error("Error loading interview:", e);
        setError(e instanceof Error ? e.message : "An unknown error occurred");
      } finally {
        setLoading(false);
      }
    };

    loadInterview();
  }, [interviewId]);

  const handleCopy = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Copied to clipboard!");
    } catch (e) {
      console.error("Failed to copy to clipboard:", e);
      toast.error("Failed to copy to clipboard");
    }
  }, []);

  const renderScoreStars = (score: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-5 h-5 ${
              i < Math.round(score / 2)
                ? "text-yellow-400 fill-current"
                : "text-gray-300"
            }`}
          />
        ))}
        <span className="ml-2 text-sm font-medium text-gray-700">
          {score.toFixed(1)}/10
        </span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-4 sm:p-6 lg:p-8">
        <div className="space-y-8">
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-10 w-32" />
          </div>
          <Skeleton className="h-8 w-1/2" />
          <Skeleton className="h-32 w-full" />
          <div className="grid md:grid-cols-2 gap-6">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-4 sm:p-6 lg:p-8">
        <div className="text-center py-12">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Error Loading Report
          </h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="flex justify-center gap-4">
            <Button onClick={() => window.location.reload()}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard/interview">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Interviews
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-4 sm:p-6 lg:p-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Interview Report</h1>
          {overallScore !== null && (
            <div className="mt-2">
              <span className="text-lg font-medium text-gray-700">
                Overall Score:{" "}
              </span>
              {renderScoreStars(overallScore)}
            </div>
          )}
        </div>
        <Button variant="outline" asChild>
          <Link href="/dashboard/interview">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Interviews
          </Link>
        </Button>
      </div>

      <div className="grid md:grid-cols-2 gap-6 mb-6">
        <div className="bg-white p-6 rounded-lg shadow-md border border-green-100">
          <h2 className="text-2xl font-semibold text-gray-700 mb-4 flex items-center">
            <CheckCircle className="text-green-500 mr-2" /> Strengths
          </h2>
          <ul className="space-y-3">
            {report?.strengths?.length ? (
              report.strengths.map((s, i) => (
                <li key={i} className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-600">{s}</span>
                </li>
              ))
            ) : (
              <li className="text-gray-500">No strengths recorded</li>
            )}
          </ul>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border border-amber-50">
          <h2 className="text-2xl font-semibold text-gray-700 mb-4 flex items-center">
            <TrendingUp className="text-amber-500 mr-2" /> Areas for Improvement
          </h2>
          <ul className="space-y-3">
            {report?.areasForImprovement?.length ? (
              report.areasForImprovement.map((s, i) => (
                <li key={i} className="flex items-start">
                  <TrendingUp className="w-5 h-5 text-amber-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-600">{s}</span>
                </li>
              ))
            ) : (
              <li className="text-gray-500">
                No areas for improvement recorded
              </li>
            )}
          </ul>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">
          Question Analysis
        </h2>
        {report?.questionAnalysis?.length ? (
          <div className="space-y-6">
            {report.questionAnalysis.map((qa, i) => (
              <div
                key={i}
                className="bg-white p-6 rounded-lg shadow-md border border-gray-100"
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <span className="inline-block px-3 py-1 text-xs font-semibold text-blue-700 bg-blue-100 rounded-full mb-2">
                      {qa.category || "General"}
                    </span>
                    <h3 className="text-lg font-semibold text-gray-800">
                      {qa.question || "Question"}
                    </h3>
                  </div>
                  {qa.score !== undefined && renderScoreStars(qa.score)}
                </div>

                <div className="bg-blue-50 p-4 rounded-lg mb-4">
                  <h4 className="text-sm font-medium text-blue-700 mb-1">
                    Your Answer
                  </h4>
                  <p className="text-blue-900">
                    {qa.answer || "No answer recorded"}
                  </p>
                </div>

                <div className="bg-amber-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-amber-700 mb-1">
                    Feedback
                  </h4>
                  <p className="text-amber-900">
                    {qa.feedback || "No feedback available for this question."}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white p-6 rounded-lg shadow-md text-center text-gray-500">
            No question analysis available.
          </div>
        )}
      </div>

      <div className="mt-8 pt-6 border-t border-gray-200">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <p className="text-sm text-gray-500 text-center sm:text-left">
            This report was generated based on your interview responses.
          </p>
        </div>
      </div>
    </div>
  );
}
