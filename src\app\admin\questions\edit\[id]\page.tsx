"use client";

import type React from "react";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, Save, Loader2 } from "lucide-react";
import Link from "next/link";

const categories = [
  { value: "behavioral", label: "Behavioral" },
  { value: "technical", label: "Technical" },
  { value: "case-based", label: "Case-based" },
];

const difficulties = [
  { value: "easy", label: "Easy" },
  { value: "medium", label: "Medium" },
  { value: "hard", label: "Hard" },
];

interface Question {
  id: string;
  questionText: string;
  category: string;
  firmId: string;
  difficulty: string;
  createdAt: string;
  updatedAt: string;
}

interface Firm {
  id: string;
  name: string;
}

export default function EditQuestionPage() {
  const router = useRouter();
  const params = useParams();
  const questionId = params.id as string;

  const [isLoading, setIsLoading] = useState(true); // Initial loading for fetching data
  const [isSaving, setIsSaving] = useState(false); // For form submission
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    questionText: "",
    category: "",
    difficulty: "",
    firmName: "",
  });

  const [firms, setFirms] = useState<{ value: string; label: string }[]>([]);

  useEffect(() => {
    const fetchFirmsAndQuestion = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Fetch firms first
        const firmsResponse = await fetch("/api/admin/firms");
        if (!firmsResponse.ok) {
          throw new Error(`HTTP error! status: ${firmsResponse.status}`);
        }
        const firmsData: Firm[] = await firmsResponse.json();
        setFirms(
          firmsData.map((firm) => ({
            value: firm.name.toLowerCase(),
            label: firm.name,
          }))
        );

        // Fetch question data
        const questionResponse = await fetch(
          `/api/admin/questions?id=${questionId}`
        );
        if (!questionResponse.ok) {
          throw new Error(`HTTP error! status: ${questionResponse.status}`);
        }
        const questionData: Question[] = await questionResponse.json();

        if (questionData.length > 0) {
          const question = questionData[0];
          const firmName =
            firmsData.find((f) => f.id === question.firmId)?.name || "";
          setFormData({
            questionText: question.questionText,
            category: question.category,
            difficulty: question.difficulty,
            firmName: firmName,
          });
        } else {
          setError("Question not found.");
        }
      } catch (err: any) {
        console.error("Failed to fetch data:", err);
        setError(
          err.message || "Failed to load question data. Please try again."
        );
      } finally {
        setIsLoading(false);
      }
    };

    if (questionId) {
      fetchFirmsAndQuestion();
    }
  }, [questionId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setError(null);

    try {
      if (
        !formData.questionText ||
        !formData.category ||
        !formData.difficulty ||
        !formData.firmName
      ) {
        setError("Please fill in all required fields");
        setIsSaving(false);
        return;
      }

      const response = await fetch("/api/admin/questions", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: questionId,
          questionText: formData.questionText,
          category: formData.category,
          firmName: formData.firmName,
          difficulty: formData.difficulty,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update question");
      }

      router.push("/admin/questions");
    } catch (err: any) {
      setError(err.message || "Failed to update question. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 max-w-4xl mx-auto space-y-6 animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-1/2"></div>
        <div className="h-48 bg-gray-200 rounded"></div>
        <div className="h-32 bg-gray-200 rounded"></div>
      </div>
    );
  }

  if (error && !isLoading) {
    return (
      <div className="p-6 max-w-4xl mx-auto space-y-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={() => router.push("/admin/questions")}>
          Back to Questions
        </Button>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/questions" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Questions
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Edit Question</h1>
          <p className="text-muted-foreground">
            Modify an existing interview question
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Edit the core details of the interview question
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="questionText">Question *</Label>
              <Textarea
                id="questionText"
                placeholder="Enter the interview question..."
                value={formData.questionText}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    questionText: e.target.value,
                  }))
                }
                className="min-h-[100px]"
                required
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, category: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="difficulty">Difficulty *</Label>
                <Select
                  value={formData.difficulty}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, difficulty: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select difficulty" />
                  </SelectTrigger>
                  <SelectContent>
                    {difficulties.map((difficulty) => (
                      <SelectItem
                        key={difficulty.value}
                        value={difficulty.value}
                      >
                        {difficulty.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="firmName">Associated Firm *</Label>
              <Select
                value={formData.firmName}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, firmName: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select firm" />
                </SelectTrigger>
                <SelectContent>
                  {firms.map((firm) => (
                    <SelectItem key={firm.value} value={firm.label}>
                      {firm.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex items-center justify-end gap-4">
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving Changes...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
