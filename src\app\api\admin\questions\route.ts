import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { questions, firms } from "@/db/schema";
import { eq } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const firmName = searchParams.get("firm");
    const category = searchParams.get("category");
    const difficulty = searchParams.get("difficulty");

    let query = db.select().from(questions).$dynamic();

    if (firmName) {
      const firm = await db.select().from(firms).where(eq(firms.name, firmName)).limit(1);
      if (firm.length > 0) {
        query = query.where(eq(questions.firmId, firm[0].id));
      } else {
        return NextResponse.json({ error: "Firm not found" }, { status: 404 });
      }
    }
    if (category) {
      query = query.where(eq(questions.category, category));
    }
    if (difficulty) {
      query = query.where(eq(questions.difficulty, difficulty));
    }

    const allQuestions = await query;
    return NextResponse.json(allQuestions);
  } catch (error) {
    console.error("Error fetching questions:", error);
    return NextResponse.json({ error: "Failed to fetch questions" }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { questionText, category, firmName, difficulty } = body;

    if (!questionText || !category || !firmName || !difficulty) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Find the firm ID
    let firm = await db.select().from(firms).where(eq(firms.name, firmName)).limit(1);

    // If firm doesn't exist, create it
    if (firm.length === 0) {
      const newFirmId = uuidv4();
      await db.insert(firms).values({ id: newFirmId, name: firmName });
      firm = [{ id: newFirmId, name: firmName, createdAt: new Date(), updatedAt: new Date() }];
    }

    const newQuestion = {
      id: uuidv4(),
      questionText,
      category,
      firmId: firm[0].id,
      difficulty,
    };

    await db.insert(questions).values(newQuestion);
    return NextResponse.json(newQuestion, { status: 201 });
  } catch (error) {
    console.error("Error creating question:", error);
    return NextResponse.json({ error: "Failed to create question" }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const { id, questionText, category, firmName, difficulty } = body;

    if (!id) {
      return NextResponse.json({ error: "Question ID is required for update" }, { status: 400 });
    }

    let firmIdToUse = null;
    if (firmName) {
      let firm = await db.select().from(firms).where(eq(firms.name, firmName)).limit(1);
      if (firm.length === 0) {
        const newFirmId = uuidv4();
        await db.insert(firms).values({ id: newFirmId, name: firmName });
        firmIdToUse = newFirmId;
      } else {
        firmIdToUse = firm[0].id;
      }
    }

    const updatedQuestionData: {
      questionText?: string;
      category?: string;
      firmId?: string;
      difficulty?: string;
      updatedAt?: Date;
    } = { updatedAt: new Date() };

    if (questionText) updatedQuestionData.questionText = questionText;
    if (category) updatedQuestionData.category = category;
    if (firmIdToUse) updatedQuestionData.firmId = firmIdToUse;
    if (difficulty) updatedQuestionData.difficulty = difficulty;

    const result = await db.update(questions)
      .set(updatedQuestionData)
      .where(eq(questions.id, id))
      .returning();

    if (result.length === 0) {
      return NextResponse.json({ error: "Question not found" }, { status: 404 });
    }

    return NextResponse.json(result[0]);
  } catch (error) {
    console.error("Error updating question:", error);
    return NextResponse.json({ error: "Failed to update question" }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json({ error: "Question ID is required for deletion" }, { status: 400 });
    }

    const result = await db.delete(questions).where(eq(questions.id, id)).returning();

    if (result.length === 0) {
      return NextResponse.json({ error: "Question not found" }, { status: 404 });
    }

    return NextResponse.json({ message: "Question deleted successfully" });
  } catch (error) {
    console.error("Error deleting question:", error);
    return NextResponse.json({ error: "Failed to delete question" }, { status: 500 });
  }
}