"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Save, Loader2 } from "lucide-react"
import Link from "next/link"
import { toast } from "sonner"

export default function NewFirmPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [firmName, setFirmName] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (!firmName) {
        toast.error("Firm name is required.");
        setIsLoading(false);
        return;
      }

      const response = await fetch("/api/admin/firms", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: firmName }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create firm");
      }

      toast.success("Firm added successfully!");
      router.push("/admin/questions"); // Redirect to the main admin page
    } catch (err: any) {
      console.error("Failed to create firm:", err);
      toast.error(err.message || "Failed to add firm. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Add New Firm</h1>
          <p className="text-muted-foreground">Add a new consulting or accounting firm</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Firm Details</CardTitle>
            <CardDescription>Enter the name of the new firm</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="firmName">Firm Name *</Label>
              <Input
                id="firmName"
                placeholder="e.g., McKinsey, Bain, BCG"
                value={firmName}
                onChange={(e) => setFirmName(e.target.value)}
                required
              />
            </div>
          </CardContent>
        </Card>

        <div className="flex items-center justify-end gap-4">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Adding Firm...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Add Firm
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}