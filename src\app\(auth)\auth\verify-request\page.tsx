"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Mail, ArrowLeft } from "lucide-react";

export default function VerifyRequestPage() {
  const router = useRouter();
  const [email, setEmail] = useState<string>("");

  useEffect(() => {
    // Get email from URL params if available
    const urlParams = new URLSearchParams(window.location.search);
    const emailParam = urlParams.get("email");
    if (emailParam) {
      setEmail(emailParam);
    }
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background-primary">
      <div className="w-full max-w-md p-8 space-y-8 bg-background-secondary rounded-lg border border-border-primary">
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mb-4">
            <Mail className="w-8 h-8 text-emerald-600" />
          </div>
          <h1 className="text-2xl font-bold">Check your email</h1>
          <p className="text-text-secondary mt-2">
            {email ? (
              <>
                A sign in link has been sent to <strong>{email}</strong>
              </>
            ) : (
              "A sign in link has been sent to your email address"
            )}
          </p>
        </div>
        
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              Click the link in your email to sign in. The link will expire in 24 hours.
            </p>
          </div>
          
          <Button
            variant="outline"
            className="w-full flex items-center justify-center space-x-2"
            onClick={() => router.push("/signin")}
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to sign in</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
