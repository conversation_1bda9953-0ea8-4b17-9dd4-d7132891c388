"use client";

import { useState } from "react";
import { analyzeResume, type AnalysisResult } from "./actions";
import { toast } from "sonner";
import { Sparkles } from "lucide-react";
import { ResumeUploader } from "@/components/dashboard/resume/resume-uploader";
import { LoadingAnimation } from "@/components/dashboard/resume/loading-animation";
import { ResumeAnalysis } from "@/components/dashboard/resume/resume-analysis";

export default function ResumePage() {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(
    null
  );
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const handleFileUpload = async (file: File) => {
    setUploadedFile(file);
    setIsAnalyzing(true);
    setAnalysisResult(null);

    const formData = new FormData();
    formData.append("resume", file);

    try {
      const result = await analyzeResume(formData);

      if ("error" in result) {
        // Handle the error case
        toast.error(result.error);
        setAnalysisResult(null);
      } else {
        // Handle the success case
        setAnalysisResult(result.analysis);
        toast.success("Resume analysis completed successfully!");
      }
    } catch (error) {
      console.error("Error calling analyzeResume server action:", error);
      toast.error(
        "An error occurred while analyzing your resume. Please try again."
      );
      setAnalysisResult(null);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleReset = () => {
    setUploadedFile(null);
    setAnalysisResult(null);
    setIsAnalyzing(false);
  };

  return (
    <main className="min-h-screen bg-gray-50 py-8 px-4 flex flex-col items-center">
      <div className="w-full max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Resume Analysis
          </h1>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          {!uploadedFile && !isAnalyzing && !analysisResult && (
            <ResumeUploader onFileUpload={handleFileUpload} />
          )}

          {isAnalyzing && <LoadingAnimation fileName={uploadedFile?.name} />}

          {analysisResult && (
            <ResumeAnalysis
              result={analysisResult}
              fileName={uploadedFile?.name}
              onReset={handleReset}
            />
          )}
        </div>
        
        {!uploadedFile && !isAnalyzing && !analysisResult && (
          <p className="mt-6 text-center text-sm text-gray-500">
            Upload your resume to receive AI-powered feedback and improvement suggestions.
          </p>
        )}
        
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Your resume is processed securely and never stored.
          </p>
        </div>
      </div>
    </main>
  );
}
