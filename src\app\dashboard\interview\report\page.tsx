"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";

interface ChatMessage {
  from: "avatar" | "user";
  message: string;
  timestamp: number;
}

export default function InterviewReportPage() {
  const router = useRouter();
  const [chat, setChat] = useState<ChatMessage[]>([]);

  useEffect(() => {
    const saved = localStorage.getItem("preparify_interview_chat");
    if (saved) setChat(JSON.parse(saved));
  }, []);

  const userAnswers = chat.filter((c) => c.from === "user");

  return (
    <main className="min-h-screen bg-gray-50 py-10 px-4 flex flex-col items-center">
      <div className="w-full max-w-4xl bg-white p-8 rounded-xl shadow border border-gray-100">
        <h1 className="text-3xl font-bold mb-6 text-gray-800">Interview Report</h1>

        {userAnswers.length === 0 ? (
          <p className="text-gray-600">No answers recorded.</p>
        ) : (
          <>
            <h2 className="text-2xl font-semibold mb-4 text-emerald-600">Your Answers</h2>
            <ol className="space-y-4 mb-8 list-decimal list-inside">
              {userAnswers.map((ans, idx) => (
                <li key={idx} className="text-gray-700">
                  {ans.message}
                </li>
              ))}
            </ol>

            <h2 className="text-2xl font-semibold mb-4 text-emerald-600">Feedback & Suggestions</h2>
            <p className="text-gray-700">
              Overall, great effort! Remember to structure your answers with the STAR method and keep
              them concise. Maintain eye contact with the interviewer avatar and project confidence.
            </p>
          </>
        )}

        <Button
          onClick={() => router.push("/dashboard")}
          className="mt-8 px-6 py-3 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg font-semibold"
        >
          Back to Dashboard
        </Button>
      </div>
    </main>
  );
}
