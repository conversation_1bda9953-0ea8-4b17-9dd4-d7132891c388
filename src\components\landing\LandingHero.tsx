import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON>, Play } from "lucide-react";

export default function LandingHero() {
  return (
    <section className="relative bg-gradient-to-br from-emerald-50 via-teal-50 to-green-50 py-20 lg:py-28">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <div className="inline-flex items-center bg-white border border-emerald-200 rounded-full px-4 py-2 mb-8 shadow-sm">
            <span className="text-emerald-600 font-semibold text-sm">
              🚀 Join 10,000+ successful candidates
            </span>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Master Your{" "}
            <span className="bg-gradient-to-r from-emerald-600 via-teal-600 to-green-600 bg-clip-text text-transparent">
              Big 4
            </span>{" "}
            Interview
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-10 leading-relaxed">
            Practice with AI-powered mock interviews, get instant feedback, and
            land your dream job at Deloitte, PwC, EY, or KPMG with confidence.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              asChild
              size="lg"
              className="px-8 py-4 text-lg"
            >
              <Link href="#firms" className="inline-flex items-center group">
                Start Practicing Now
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            <Button
              variant="ghost"
              size="lg"
              className="text-emerald-600 hover:text-emerald-700 text-lg"
            >
              <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
              Watch Demo
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
