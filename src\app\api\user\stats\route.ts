import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { interviewSessions, questionResponses, interviewReports } from "@/db/schema";
import { eq, count, and } from "drizzle-orm";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get total completed interviews
    const completedInterviews = await db
      .select({ count: count() })
      .from(interviewSessions)
      .where(
        and(
          eq(interviewSessions.userId, session.user.id),
          eq(interviewSessions.status, "completed")
        )
      );

    // Get total questions answered
    const questionsAnswered = await db
      .select({ count: count() })
      .from(questionResponses)
      .innerJoin(interviewSessions, eq(questionResponses.sessionId, interviewSessions.id))
      .where(eq(interviewSessions.userId, session.user.id));

    // Get average score from completed interviews
    const averageScoreResult = await db
      .select({ avgScore: interviewReports.overallScore })
      .from(interviewReports)
      .innerJoin(interviewSessions, eq(interviewReports.sessionId, interviewSessions.id))
      .where(eq(interviewSessions.userId, session.user.id));

    const averageScore = averageScoreResult.length > 0 
      ? averageScoreResult.reduce((sum, r) => sum + r.avgScore, 0) / averageScoreResult.length
      : 0;

    // Get recent interview sessions for streak calculation
    const recentSessions = await db
      .select({ startedAt: interviewSessions.startedAt })
      .from(interviewSessions)
      .where(
        and(
          eq(interviewSessions.userId, session.user.id),
          eq(interviewSessions.status, "completed")
        )
      )
      .orderBy(interviewSessions.startedAt);

    // Calculate practice streak (consecutive days with interviews)
    let practiceStreak = 0;
    if (recentSessions.length > 0) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      let currentDate = new Date(today);
      let hasInterviewToday = false;
      
      // Check if there's an interview today
      const todayInterviews = recentSessions.filter(s => {
        const sessionDate = new Date(s.startedAt);
        sessionDate.setHours(0, 0, 0, 0);
        return sessionDate.getTime() === today.getTime();
      });
      
      if (todayInterviews.length > 0) {
        hasInterviewToday = true;
        practiceStreak = 1;
      }
      
      // Count consecutive days backwards
      for (let i = hasInterviewToday ? 1 : 0; i < 30; i++) { // Check up to 30 days back
        currentDate.setDate(currentDate.getDate() - 1);
        
        const dayInterviews = recentSessions.filter(s => {
          const sessionDate = new Date(s.startedAt);
          sessionDate.setHours(0, 0, 0, 0);
          return sessionDate.getTime() === currentDate.getTime();
        });
        
        if (dayInterviews.length > 0) {
          practiceStreak++;
        } else {
          break; // Streak broken
        }
      }
    }

    const stats = {
      totalInterviews: completedInterviews[0]?.count || 0,
      totalQuestions: questionsAnswered[0]?.count || 0,
      averageScore: Math.round(averageScore * 10) / 10,
      practiceStreak,
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error("Error fetching user stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
