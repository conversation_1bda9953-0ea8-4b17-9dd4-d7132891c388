"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, FileText, BarChart3, Settings } from "lucide-react";

export default function AdminDashboard() {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Main Content */}
        <div className="text-center mb-12">
          <div className="bg-white rounded-lg shadow-sm border p-12 mb-8">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">
              Admin Portal
            </h2>
            <p className="text-xl text-gray-600 mb-6">
              This is the admin dashboard to manage questions, categories, and
              interview content
            </p>
            <Badge variant="secondary" className="text-sm px-3 py-1">
              Question Management System
            </Badge>
          </div>
        </div>

        {/* Quick Actions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => {
              window.location.href = "/admin/questions/new";
            }}
          >
            <CardHeader className="text-center pb-4">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Plus className="w-6 h-6 text-blue-600" />
              </div>
              <CardTitle className="text-lg">Add Questions</CardTitle>
              <CardDescription>
                Create new interview questions for Preparify
              </CardDescription>
            </CardHeader>
          </Card>

          <Card
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => {
              window.location.href = "/admin/questions";
            }}
          >
            <CardHeader className="text-center pb-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <FileText className="w-6 h-6 text-green-600" />
              </div>
              <CardTitle className="text-lg">View questions</CardTitle>
              <CardDescription>
                Edit existing questions and frims
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    </div>
  );
}
