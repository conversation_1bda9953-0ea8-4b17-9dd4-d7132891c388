import React from "react";

import { useVoiceChat } from "../logic/useVoiceChat";
import { useConversationState } from "../logic/useConversationState";
import { Button } from "@/components/ui/button";
import { Loader2Icon, MicIcon, MicOffIcon } from "lucide-react";

export const AudioInput: React.FC = () => {
  const { muteInputAudio, unmuteInputAudio, isMuted, isVoiceChatLoading } =
    useVoiceChat();
  const { isUserTalking } = useConversationState();

  const handleMuteClick = () => {
    if (isMuted) {
      unmuteInputAudio();
    } else {
      muteInputAudio();
    }
  };

  return (
    <div>
      <Button
        className={`!p-2 relative`}
        disabled={isVoiceChatLoading}
        onClick={handleMuteClick}
      >
        <div
          className={`absolute left-0 top-0 rounded-lg border-2 border-[#7559FF] w-full h-full ${
            isUserTalking ? "animate-ping" : ""
          }`}
        />
        {isVoiceChatLoading ? (
          <Loader2Icon className="animate-spin" size={20} />
        ) : isMuted ? (
          <MicOffIcon size={20} />
        ) : (
          <MicIcon size={20} />
        )}
      </Button>
    </div>
  );
};
