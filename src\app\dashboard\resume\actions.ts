"use server";

import { z } from "zod";

// Enhanced schema for comprehensive resume analysis
const ResumeAnalysisSchema = z.object({
  rating: z.number().int().min(0).max(100),
  generalFeedback: z.string(),
  industrySpecificFeedback: z.string(),
  atsImprovements: z.array(
    z.object({
      category: z.string(),
      priority: z.enum(["Critical", "High", "Medium", "Low"]),
      suggestions: z.array(z.string()),
      impact: z.string(),
    })
  ),
  strengths: z.array(z.string()),
  weaknesses: z.array(z.string()),
  detailedScoring: z.object({
    contentQuality: z.number().int().min(0).max(100),
    formatting: z.number().int().min(0).max(100),
    atsCompatibility: z.number().int().min(0).max(100),
    industryAlignment: z.number().int().min(0).max(100),
    achievementQuantification: z.number().int().min(0).max(100),
    keywordOptimization: z.number().int().min(0).max(100),
  }),
  careerLevel: z.enum(["Entry", "Mid", "Senior", "Executive"]),
  industryFocus: z.string(),
  recommendedActions: z.array(
    z.object({
      action: z.string(),
      priority: z.enum(["Critical", "High", "Medium", "Low"]),
      timeToImplement: z.string(),
      expectedImpact: z.string(),
    })
  ),
});

export type AnalysisResult = z.infer<typeof ResumeAnalysisSchema>;

// Extract text from PDF using pdf-parse
async function extractTextFromPDF(pdfBuffer: Buffer): Promise<string> {
  try {
    const pdfParse = (await import("pdf-parse")).default;

    if (!Buffer.isBuffer(pdfBuffer) || pdfBuffer.length === 0) {
      throw new Error("Invalid PDF buffer provided");
    }

    const pdfHeader = pdfBuffer.slice(0, 4).toString();
    if (pdfHeader !== "%PDF") {
      console.error("Buffer doesn't start with PDF header:", pdfHeader);
      throw new Error("Invalid PDF format - missing PDF header");
    }

    const data = await pdfParse(pdfBuffer);

    if (!data.text || data.text.trim().length === 0) {
      throw new Error(
        "No text found in PDF - document may be image-based or corrupted"
      );
    }

    return data.text;
  } catch (error) {
    console.error("Error extracting text from PDF:", error);

    if (error instanceof Error) {
      if (error.message.includes("ENOENT")) {
        throw new Error(
          "PDF processing error: File access issue. Please try uploading the file again."
        );
      }
      if (error.message.includes("Invalid PDF format")) {
        throw new Error(
          "Invalid PDF format. Please ensure the file is a valid PDF document."
        );
      }
      if (error.message.includes("No text found")) {
        throw new Error(
          "Cannot extract text from PDF. The document may be image-based or password-protected."
        );
      }
    }

    throw new Error(
      "Failed to extract text from PDF. Please try a different PDF file."
    );
  }
}

export async function analyzeResume(
  formData: FormData
): Promise<{ success: true; analysis: AnalysisResult } | { error: string }> {
  const file = formData.get("resume") as File;

  if (!file) {
    return { error: "No file uploaded." };
  }

  const allowedTypes = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "image/webp",
  ];
  if (!allowedTypes.includes(file.type)) {
    return {
      error: "Invalid file type. Please upload a PDF, JPG, PNG, or WebP file.",
    };
  }

  const maxSize =
    file.type === "application/pdf" ? 10 * 1024 * 1024 : 4 * 1024 * 1024;
  if (file.size > maxSize) {
    return {
      error: `File size too large. Please upload a file smaller than ${
        file.type === "application/pdf" ? "10MB" : "4MB"
      }.`,
    };
  }

  let resumeContent = "";
  let contentType = "";

  try {
    const arrayBuffer = await file.arrayBuffer();

    if (file.type === "application/pdf") {
      const pdfBuffer = Buffer.from(arrayBuffer);

      resumeContent = await extractTextFromPDF(pdfBuffer);
      contentType = "text";
    } else {
      const base64 = Buffer.from(arrayBuffer).toString("base64");
      resumeContent = `data:${file.type};base64,${base64}`;
      contentType = "image";
    }
  } catch (error) {
    console.error("Error processing file:", error);
    if (error instanceof Error) {
      if (error.message.includes("PDF")) {
        return { error: error.message };
      }
    }
    return { error: "Failed to process file content." };
  }

  const groqApiKey = process.env.GROQ_API_KEY;
  if (!groqApiKey) {
    console.error("GROQ_API_KEY is not set in environment variables.");
    return { error: "Server configuration error: AI API key not found." };
  }

  const models = ["llama-3.1-8b-instant"];

  for (const model of models) {
    try {
      const messages = [
        {
          role: "system",
          content: `You are a resume optimization engine purpose-built for students and professionals applying to audit, accounting, finance, and Big 4 roles. You combine 15+ years of HR and recruitment experience across consulting firms, investment banks, and Fortune 500s with deep knowledge of modern ATS (Applicant Tracking Systems). You write like Grammarly Premium and think like a Big 4 recruiter.

GOAL:
Transform good resumes into elite, interview-winning documents. Provide precise, actionable suggestions for word choice, phrasing, metrics, formatting, and ATS optimization — with a laser focus on results and relevance.

YOUR TASK:
Analyze and rewrite where necessary. Highlight:
- Weak, vague, or passive phrases (e.g. “worked at Tim Hortons as a cashier”) and suggest high-impact alternatives (e.g. “Led night shifts as acting manager; improved upsell revenue by 20% through optimized POS scripting”)
- Words or phrases that should be replaced with *ATS-friendly alternatives* based on audit, accounting, or finance job descriptions
- Lines that can be *rephrased to reflect ownership, leadership, or measurable impact*
- Formatting or structural issues that reduce scannability or ATS compatibility

WEIGHTED EVALUATION FRAMEWORK:
1. *Content Quality (25%)* – clarity, conciseness, power verbs, achievements
2. *Professional Presentation (20%)* – formatting, consistency, layout
3. *ATS Compatibility (15%)* – parsability, structure, keyword relevance
4. *Industry Alignment (20%)* – audit/finance jargon, certifications, tools
5. *Achievement Quantification (10%)* – metrics, improvements, scope
6. *Keyword Optimization (10%)* – industry and job-posting-aligned terms

INDUSTRY TARGET: 
Audit (Big 4), Corporate Finance, Assurance, Advisory, and Banking

KEY PRINCIPLES:
- Use bold action verbs: spearheaded, implemented, analyzed, optimized
- Avoid vague tasks: Replace “responsible for” with actual outcomes
- Inject metrics where possible: %, $, team size, volume, frequency
- Think like a recruiter: If a line doesn’t scream value, rewrite it

OUTPUT FORMAT (strictly valid JSON only):
{
  "rating": <number 0-100>,
  "generalFeedback": "<3-4 paragraphs of high-level analysis and advice>",
  "industrySpecificFeedback": "<2-3 paragraphs focused on audit/finance alignment>",
  "atsImprovements": [
    {
      "category": "<category name>",
      "priority": "<High/Medium/Low>",
      "suggestions": ["<specific suggestion 1>", "<specific suggestion 2>", "<specific suggestion 3>"],
      "impact": "<expected impact description>"
    }
  ],
  "strengths": [
    "<specific strength 1>",
    "<specific strength 2>",
    "<specific strength 3>"
  ],
  "weaknesses": [
    "<specific weakness 1>",
    "<specific weakness 2>",
    "<specific weakness 3>"
  ],
  "detailedScoring": {
    "contentQuality": <0-100>,
    "formatting": <0-100>,
    "atsCompatibility": <0-100>,
    "industryAlignment": <0-100>,
    "achievementQuantification": <0-100>,
    "keywordOptimization": <0-100>
  },
  "careerLevel": "<Entry/Mid/Senior/Executive>",
  "industryFocus": "<detected industry focus>",
  "recommendedActions": [
    {
      "action": "<clear action to improve resume>",
      "priority": "<Critical/High/Medium/Low>",
      "timeToImplement": "<estimated time>",
      "expectedImpact": "<impact on job-readiness or recruiter impression>"
    }
  ]
}

RULES:
- No external text, commentary, or explanations
- JSON must use double quotes only and valid formatting
- Focus on measurable improvements and clear rewordings
- Assume user is applying to Big 4 and similar high-performance audit/finance roles`,
        },
        {
          role: "user",
          content:
            contentType === "text"
              ? `Analyze this resume content and provide feedback in the exact JSON format specified. Resume content: ${resumeContent.substring(
                  0,
                  3000
                )}` // Limit content to avoid token limits
              : [
                  {
                    type: "text",
                    text: "Analyze this resume image and provide feedback in the exact JSON format specified.",
                  },
                  {
                    type: "image_url",
                    image_url: {
                      url: resumeContent,
                    },
                  },
                ],
        },
      ];

      const requestBody = {
        model: model,
        messages: messages,
        max_tokens: 1500,
        temperature: 0.1, // Lower temperature for more consistent JSON
        response_format: { type: "json_object" },
      };

      const response = await fetch(
        "https://api.groq.com/openai/v1/chat/completions",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${groqApiKey}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        const errorBody = await response.text();
        console.error(
          `Groq API error with ${model}: ${response.status} - ${response.statusText}`,
          errorBody
        );
        continue;
      }

      const data = await response.json();

      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        console.error("Invalid response structure:", data);
        continue;
      }

      let analysisContent = data.choices[0].message.content;

      if (typeof analysisContent !== "string") {
        console.error("AI response content is not a string:", analysisContent);
        continue;
      }

      try {
        const parsedContent = JSON.parse(analysisContent);
        const validatedAnalysis = ResumeAnalysisSchema.parse(parsedContent);
        return { success: true, analysis: validatedAnalysis };
      } catch (parseError) {
        console.error(`Failed to parse AI response from ${model}:`, parseError);
        console.error("Raw content:", analysisContent);
        continue;
      }
    } catch (error) {
      console.error(`Error during AI analysis with ${model}:`, error);
      continue;
    }
  }

  return { error: "Failed to analyze resume. Please try again." };
}
