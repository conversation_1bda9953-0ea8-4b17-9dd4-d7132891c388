import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { interviewSessions, firms } from "@/db/schema";
import { eq, sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { category, firmName, durationMinutes = 2 } = body;

    if (!category || !firmName) {
      return NextResponse.json(
        { error: "Category and firm are required" },
        { status: 400 }
      );
    }

    // Get the firm ID
    const firm = await db
      .select()
      .from(firms)
      .where(eq(sql`LOWER(${firms.name})`, firmName.toLowerCase()))
      .limit(1);

    if (firm.length === 0) {
      return NextResponse.json({ error: "Firm not found" }, { status: 404 });
    }

    // Create new interview session
    const sessionId = uuidv4();
    const newSession = {
      id: sessionId,
      userId: session.user.id,
      category,
      firmId: firm[0].id,
      status: "in_progress",
      durationMinutes,
    };

    console.log("Creating session:", sessionId, "for user:", session.user.id);
    await db.insert(interviewSessions).values(newSession);
    console.log("Session created successfully");

    return NextResponse.json({
      sessionId,
      message: "Interview session created successfully",
    });
  } catch (error) {
    console.error("Error creating interview session:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { sessionId, status, completedAt } = body;

    if (!sessionId) {
      return NextResponse.json(
        { error: "Session ID is required" },
        { status: 400 }
      );
    }

    // Update interview session
    const updateData: any = {};
    if (status) updateData.status = status;
    if (completedAt) updateData.completedAt = new Date(completedAt);
    updateData.updatedAt = new Date();

    await db
      .update(interviewSessions)
      .set(updateData)
      .where(eq(interviewSessions.id, sessionId));

    return NextResponse.json({
      message: "Interview session updated successfully",
    });
  } catch (error) {
    console.error("Error updating interview session:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
