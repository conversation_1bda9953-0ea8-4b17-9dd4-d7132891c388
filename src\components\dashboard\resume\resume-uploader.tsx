"use client";

import { useState, useCallback } from "react";
import { Upload, FileText, Image, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ResumeUploaderProps {
  onFileUpload: (file: File) => void;
}

export function ResumeUploader({ onFileUpload }: ResumeUploaderProps) {
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string>("");

  const validateFile = (file: File): string | null => {
    const allowedTypes = ["application/pdf", "image/jpeg", "image/png", "image/webp"];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
      return "Please upload a PDF, JPG, PNG, or WebP file.";
    }

    if (file.size > maxSize) {
      return "File size must be less than 10MB.";
    }

    return null;
  };

  const handleFiles = useCallback((files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    const validationError = validateFile(file);

    if (validationError) {
      setError(validationError);
      return;
    }

    setError("");
    onFileUpload(file);
  }, [onFileUpload]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    handleFiles(e.dataTransfer.files);
  }, [handleFiles]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    handleFiles(e.target.files);
  }, [handleFiles]);

  return (
    <div className="p-8 md:p-12">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-emerald-100 rounded-full mb-4">
          <Upload className="w-8 h-8 text-emerald-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Upload Your Resume</h2>
        <p className="text-gray-600">
          Get instant AI-powered feedback to optimize your resume for ATS systems
        </p>
      </div>

      <div
        className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 ${
          dragActive
            ? "border-emerald-400 bg-emerald-50"
            : "border-gray-300 hover:border-emerald-400 hover:bg-gray-50"
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          id="resume-upload"
          accept=".pdf,.jpg,.jpeg,.png,.webp"
          onChange={handleChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />

        <div className="space-y-4">
          <div className="flex justify-center space-x-4">
            <div className="flex items-center space-x-2 px-4 py-2 bg-blue-100 rounded-lg">
              <FileText className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">PDF</span>
            </div>
            <div className="flex items-center space-x-2 px-4 py-2 bg-purple-100 rounded-lg">
              <Image className="w-5 h-5 text-purple-600" aria-label="Images" />
              <span className="text-sm font-medium text-purple-800">Images</span>
            </div>
          </div>

          <div>
            <p className="text-lg font-medium text-gray-900 mb-2">
              Drop your resume here or click to browse
            </p>
            <p className="text-sm text-gray-500">
              Supports PDF, JPG, PNG, WebP • Max size: 10MB
            </p>
          </div>

          <Button
            type="button"
            onClick={() => document.getElementById("resume-upload")?.click()}
            className="inline-flex items-center px-6 py-3 bg-emerald-600 text-white font-medium rounded-lg hover:bg-emerald-700 transition-colors duration-200"
          >
            <Upload className="w-5 h-5 mr-2" />
            Choose File
          </Button>
        </div>
      </div>

      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start space-x-3">
          <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}

      <div className="mt-8 text-center">
        <p className="text-sm text-gray-500 mb-4">
          ✨ Get insights on keyword optimization, ATS compatibility, and formatting improvements
        </p>
        <div className="flex flex-wrap justify-center gap-2 text-xs text-gray-400">
          <span className="px-3 py-1 bg-gray-100 rounded-full">Keyword Analysis</span>
          <span className="px-3 py-1 bg-gray-100 rounded-full">ATS Scoring</span>
          <span className="px-3 py-1 bg-gray-100 rounded-full">Format Check</span>
          <span className="px-3 py-1 bg-gray-100 rounded-full">Content Review</span>
        </div>
      </div>
    </div>
  );
}